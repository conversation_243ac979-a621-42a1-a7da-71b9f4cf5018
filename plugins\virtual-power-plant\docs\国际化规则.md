AI 代码生成国际化规则（最新版）
规则说明
所有界面/业务写死中文（如提示语、按钮文本等）必须用 $T('中文') 包裹，参数为原始字符串。
所有中英文对照字段必须写入 src/config/lang/en.json，结构为 { "中文": "英文" }，如无则自动新增。
英文 value 由 AI 自动翻译生成。
代码生成前: console.log("谐波电压频谱"); 代码生成后: console.log($T("谐波电压频谱")); en.json 自动新增：{ "谐波电压频谱": "Harmonic Voltage Spectrum" }
如遇特殊名词或缩写，优先保留原文或人工校对。
本规则适用于所有新生成代码，AI 需严格遵循。
目录结构
业务代码：所有写死中文用 $T 包裹
国际化资源：src/config/lang/en.json（所有中英文对照字段必须写入此文件）
示例

<!-- 生成前 -->

<button>+ 添加用户</button>

<h2>谐波电压频谱</h2>

<!-- 生成后 -->

<button>{{ $T('+ 添加用户') }}</button>

<h2>{{ $T('谐波电压频谱') }}</h2>
en.json:

{
"+ 添加用户": "+ Add User",
"谐波电压频谱": "Harmonic Voltage Spectrum"
}
进阶用法

<!-- 属性绑定、指令等场景 -->

<el-button :title="$T('点击添加用户')">{{ $T('+ 添加用户') }}</el-button>
<span v-if="show">{{ $T('显示内容') }}</span>
