# 全局配置

## 概述

本文档详细介绍了三个组件库的全局配置方法和可配置选项。

## EEM-BASE 组件库配置

### 组件注册

```javascript
// main.js
import { CustomElSelect, CustomElDatePicker, CustomSteps, UploadDialog } from "eem-base/components";

// 全局注册组件
Vue.component("CustomElDatePicker", CustomElDatePicker);
Vue.component("customElSelect", CustomElSelect);
Vue.component("CustomElSelect", CustomElSelect);
Vue.component("customSteps", CustomSteps);
Vue.component("UploadDialog", UploadDialog);
```

### 按需引入

```javascript
// 在组件中按需引入
import { CustomElSelect } from "eem-base/components";

export default {
  components: {
    CustomElSelect
  }
};
```

## CET-COMMON 组件库配置

### 全局安装

```javascript
// main.js
import CetCommon from "cet-common";

Vue.use(CetCommon, {
  // 全局配置选项
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  },
  CetGiantTree: {
    isAutoCancelSelected: true
  },
  CetDateSelect: {
    isClosedEnd: true
  }
});
```

### 配置选项详解

#### API 配置
```javascript
// 自定义 API 实例
import axios from 'axios';

const customApi = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

Vue.use(CetCommon, {
  api: customApi
});
```

#### CetDialog 配置
```javascript
Vue.use(CetCommon, {
  CetDialog: {
    isDraggable: false,        // 是否可拖拽，默认 false
    defaultWidth: '960px',     // 默认宽度
    defaultTitle: '弹窗',      // 默认标题
    closeOnClickModal: false,  // 点击遮罩是否关闭
    closeOnPressEscape: false, // 按ESC是否关闭
    showClose: false          // 是否显示关闭按钮
  }
});
```

#### CetTable 配置
```javascript
Vue.use(CetCommon, {
  CetTable: {
    isColumsHeaderDoLayout: true,  // 是否自动调整列宽
    defaultPageSize: 20,           // 默认分页大小
    showPagination: true,          // 是否显示分页器
    border: true,                  // 是否显示边框
    highlightCurrentRow: true,     // 是否高亮当前行
    stripe: false                  // 是否显示斑马纹
  }
});
```

#### CetGiantTree 配置
```javascript
Vue.use(CetCommon, {
  CetGiantTree: {
    isAutoCancelSelected: true,    // 是否允许 Ctrl 取消选择
    showFilter: true,              // 是否显示搜索框
    openShiftCheck: true,          // 是否支持 Shift 多选
    defaultExpandLevel: 2          // 默认展开层级
  }
});
```

#### CetDateSelect 配置
```javascript
Vue.use(CetCommon, {
  CetDateSelect: {
    isClosedEnd: true,             // 是否闭合结束
    defaultLayout: 'select',       // 默认布局模式
    showButton: true,              // 是否显示前后切换按钮
    nextBtnDisabled: true,         // 下一个按钮是否禁用
    defaultTypeList: ['day', 'week', 'month', 'season', 'year']
  }
});
```

#### CetButton 配置
```javascript
Vue.use(CetCommon, {
  CetButton: {
    defaultSize: 'small',          // 默认尺寸
    defaultType: 'default'         // 默认类型
  }
});
```

#### CetForm 配置
```javascript
Vue.use(CetCommon, {
  CetForm: {
    labelWidth: '100px',           // 默认标签宽度
    labelPosition: 'right',        // 标签位置
    size: 'small',                 // 表单尺寸
    validateOnRuleChange: true     // 规则改变时是否验证
  }
});
```

### 按需引入配置

```javascript
// 按需引入单个组件
import { CetButton, CetDialog, CetTable } from "cet-common";

export default {
  components: {
    CetButton,
    CetDialog,
    CetTable
  }
};
```

## CET-CHART 图表组件库配置

### 全局安装

```javascript
// main.js
import CetChart, { registerTheme } from "cet-chart";

Vue.use(CetChart, {
  // 主题配置
  themeConf: {
    backgroundColor: "#000",
    textStyle: {
      color: "#fff"
    }
  },
  // 默认配置
  defaultOptions: {
    animation: true,
    animationDuration: 1000
  }
});
```

### 主题配置

#### 注册自定义主题
```javascript
import { registerTheme } from "cet-chart";

// 注册自定义主题
registerTheme('custom', {
  color: [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', 
    '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'
  ],
  backgroundColor: '#f8f9fa',
  textStyle: {
    color: '#333',
    fontFamily: 'Arial, sans-serif'
  },
  title: {
    textStyle: {
      color: '#333',
      fontSize: 18
    }
  },
  legend: {
    textStyle: {
      color: '#666'
    }
  },
  grid: {
    borderColor: '#eee'
  }
});
```

#### 内置主题
```javascript
// 可用的内置主题
const themes = ['dark', 'light', 'blue', 'bluex'];

// 主题会根据 localStorage 中的 omega_theme 值自动切换
localStorage.setItem('omega_theme', 'dark');
```

### ECharts 实例配置

```javascript
// 导入 ECharts 实例进行自定义配置
import echarts from "cet-chart/echarts";

// 注册自定义图表类型或组件
echarts.registerMap('china', chinaMapData);

// 全局配置
echarts.registerTheme('myTheme', {
  // 主题配置
});
```

## 环境配置

### 开发环境配置

```javascript
// main.js
if (process.env.NODE_ENV === 'development') {
  Vue.use(CetCommon, {
    debug: true,                   // 开启调试模式
    CetTable: {
      showDebugInfo: true          // 显示调试信息
    }
  });
}
```

### 生产环境配置

```javascript
// main.js
if (process.env.NODE_ENV === 'production') {
  Vue.use(CetCommon, {
    debug: false,                  // 关闭调试模式
    performance: {
      enableOptimization: true     // 启用性能优化
    }
  });
}
```

## 国际化配置

### 多语言支持

```javascript
// main.js
import VueI18n from 'vue-i18n';
import CetCommon from "cet-common";

Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: 'zh-CN',
  messages: {
    'zh-CN': {
      cet: {
        button: {
          confirm: '确定',
          cancel: '取消'
        },
        table: {
          noData: '暂无数据'
        }
      }
    },
    'en-US': {
      cet: {
        button: {
          confirm: 'Confirm',
          cancel: 'Cancel'
        },
        table: {
          noData: 'No Data'
        }
      }
    }
  }
});

Vue.use(CetCommon, {
  i18n: i18n
});
```

## 样式配置

### 自定义主题样式

```scss
// styles/cet-theme.scss

// 覆盖组件默认样式
.cet-button {
  border-radius: 6px;
  
  &.cet-button--primary {
    background-color: #007bff;
    border-color: #007bff;
  }
}

.cet-dialog {
  .el-dialog__header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e4e7ed;
  }
}

.cet-table {
  .el-table__header {
    background-color: #fafafa;
  }
}
```

### CSS 变量配置

```css
/* styles/cet-variables.css */
:root {
  --cet-primary-color: #409EFF;
  --cet-success-color: #67C23A;
  --cet-warning-color: #E6A23C;
  --cet-danger-color: #F56C6C;
  --cet-info-color: #909399;
  
  --cet-border-radius: 4px;
  --cet-font-size-base: 14px;
  --cet-line-height-base: 1.5;
}
```

## 配置验证

### 配置检查工具

```javascript
// utils/config-validator.js
export function validateCetConfig(config) {
  const errors = [];
  
  // 检查必需配置
  if (!config.api) {
    errors.push('API instance is required');
  }
  
  // 检查组件配置
  if (config.CetDialog && typeof config.CetDialog.isDraggable !== 'boolean') {
    errors.push('CetDialog.isDraggable must be boolean');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 使用配置验证
const config = {
  api: customApi,
  CetDialog: {
    isDraggable: false
  }
};

const validation = validateCetConfig(config);
if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

## 最佳实践

### 配置管理

1. **集中配置管理**：将所有组件库配置集中在一个配置文件中
2. **环境区分**：根据不同环境使用不同的配置
3. **配置验证**：在应用启动时验证配置的正确性
4. **文档同步**：保持配置文档与实际配置的同步

### 性能优化

1. **按需加载**：只加载需要的组件
2. **主题缓存**：缓存主题配置避免重复计算
3. **配置缓存**：缓存全局配置避免重复解析

### 维护建议

1. **版本控制**：记录配置变更历史
2. **配置备份**：定期备份重要配置
3. **监控告警**：监控配置异常情况
4. **文档更新**：及时更新配置文档

## 相关文档

- [README](./README.md) - 组件库总体介绍
- [最佳实践](./最佳实践.md) - 使用建议和注意事项
