# UploadDialog - 上传对话框

## 组件概述

`UploadDialog` 是一个文件上传对话框组件，提供了文件上传、模板下载等功能，特别适用于批量数据导入场景。

## 组件名称

- `UploadDialog`

## 基本用法

```vue
<template>
  <div>
    <el-button @click="openUpload">批量导入</el-button>
    
    <UploadDialog
      :openTrigger_in="openTrigger"
      :closeTrigger_in="closeTrigger"
      :extensionNameList_in="['.xlsx', '.xls']"
      dialogTitle="批量导入用户"
      :maxFlinkCount="1000"
      @uploadFile="handleUpload"
      @download="handleDownload"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      openTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openUpload() {
      this.openTrigger = Date.now();
    },
    closeUpload() {
      this.closeTrigger = Date.now();
    },
    handleUpload(file) {
      console.log('上传文件:', file);
      // 处理文件上传逻辑
      this.uploadFile(file).then(() => {
        this.$message.success('上传成功');
        this.closeUpload();
      }).catch(error => {
        this.$message.error('上传失败: ' + error.message);
      });
    },
    handleDownload() {
      console.log('下载模板');
      // 处理模板下载逻辑
      this.downloadTemplate();
    },
    async uploadFile(file) {
      // 实际的文件上传逻辑
      const formData = new FormData();
      formData.append('file', file);
      return await this.$api.uploadFile(formData);
    },
    downloadTemplate() {
      // 实际的模板下载逻辑
      window.open('/api/download/template');
    }
  }
};
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| extensionNameList_in | Array | - | 支持的文件扩展名列表（必需） |
| openTrigger_in | Number | 0 | 打开触发器（时间戳） |
| closeTrigger_in | Number | 0 | 关闭触发器（时间戳） |
| hideDownload | Boolean | false | 是否隐藏下载模板按钮 |
| dialogTitle | String | '上传文件' | 对话框标题 |
| downloadPermission | String | - | 下载权限标识 |
| maxFlinkCount | Number | - | 最大导入数据量 |

### 属性详细说明

#### extensionNameList_in (必需)
支持的文件扩展名数组，用于限制用户只能上传指定类型的文件：
```javascript
// 只支持Excel文件
extensionNameList_in: ['.xlsx', '.xls']

// 支持多种文件类型
extensionNameList_in: ['.xlsx', '.xls', '.csv', '.txt']
```

#### openTrigger_in / closeTrigger_in
使用时间戳控制对话框的打开和关闭：
```javascript
// 打开对话框
this.openTrigger = Date.now();

// 关闭对话框
this.closeTrigger = Date.now();
```

#### maxFlinkCount
限制导入数据的最大数量，超过此数量会给出提示：
```javascript
maxFlinkCount: 1000 // 最多导入1000条数据
```

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| uploadFile | file | 文件上传事件，参数为选择的文件对象 |
| download | - | 模板下载事件 |

## 使用示例

### Excel文件导入

```vue
<template>
  <div>
    <el-button type="primary" @click="openExcelImport">导入Excel</el-button>
    
    <UploadDialog
      :openTrigger_in="excelTrigger"
      :closeTrigger_in="closeTrigger"
      :extensionNameList_in="['.xlsx', '.xls']"
      dialogTitle="Excel数据导入"
      :maxFlinkCount="5000"
      @uploadFile="handleExcelUpload"
      @download="downloadExcelTemplate"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      excelTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openExcelImport() {
      this.excelTrigger = Date.now();
    },
    async handleExcelUpload(file) {
      try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await this.$api.importExcel(formData);
        
        if (response.success) {
          this.$message.success(`成功导入 ${response.count} 条数据`);
          this.closeTrigger = Date.now();
          this.$emit('refresh'); // 刷新列表
        } else {
          this.$message.error(response.message);
        }
      } catch (error) {
        this.$message.error('导入失败: ' + error.message);
      }
    },
    downloadExcelTemplate() {
      // 下载Excel模板
      const link = document.createElement('a');
      link.href = '/api/template/excel';
      link.download = '导入模板.xlsx';
      link.click();
    }
  }
};
</script>
```

### 图片批量上传

```vue
<template>
  <div>
    <el-button @click="openImageUpload">上传图片</el-button>
    
    <UploadDialog
      :openTrigger_in="imageTrigger"
      :closeTrigger_in="closeTrigger"
      :extensionNameList_in="['.jpg', '.jpeg', '.png', '.gif']"
      dialogTitle="批量上传图片"
      :hideDownload="true"
      @uploadFile="handleImageUpload"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      imageTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openImageUpload() {
      this.imageTrigger = Date.now();
    },
    async handleImageUpload(file) {
      try {
        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) {
          this.$message.error('图片大小不能超过5MB');
          return;
        }
        
        const formData = new FormData();
        formData.append('image', file);
        
        const response = await this.$api.uploadImage(formData);
        
        if (response.success) {
          this.$message.success('图片上传成功');
          this.closeTrigger = Date.now();
          this.$emit('imageUploaded', response.url);
        }
      } catch (error) {
        this.$message.error('上传失败: ' + error.message);
      }
    }
  }
};
</script>
```

### 带权限控制的上传

```vue
<template>
  <div>
    <el-button 
      @click="openUpload" 
      v-permission="'data:import'"
    >
      数据导入
    </el-button>
    
    <UploadDialog
      :openTrigger_in="uploadTrigger"
      :closeTrigger_in="closeTrigger"
      :extensionNameList_in="['.csv']"
      dialogTitle="CSV数据导入"
      downloadPermission="template:download"
      @uploadFile="handleCsvUpload"
      @download="downloadCsvTemplate"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      uploadTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openUpload() {
      this.uploadTrigger = Date.now();
    },
    async handleCsvUpload(file) {
      // CSV文件处理逻辑
      try {
        const text = await this.readFileAsText(file);
        const data = this.parseCsv(text);
        
        await this.$api.importCsvData(data);
        this.$message.success('CSV导入成功');
        this.closeTrigger = Date.now();
      } catch (error) {
        this.$message.error('CSV导入失败: ' + error.message);
      }
    },
    downloadCsvTemplate() {
      // 下载CSV模板
      const csvContent = 'name,age,email\n示例姓名,25,<EMAIL>';
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'template.csv';
      link.click();
      URL.revokeObjectURL(url);
    },
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = reject;
        reader.readAsText(file);
      });
    },
    parseCsv(text) {
      // 简单的CSV解析逻辑
      const lines = text.split('\n');
      const headers = lines[0].split(',');
      return lines.slice(1).map(line => {
        const values = line.split(',');
        const obj = {};
        headers.forEach((header, index) => {
          obj[header] = values[index];
        });
        return obj;
      });
    }
  }
};
</script>
```

## 适用场景

1. **文件上传功能**
   - 单文件上传
   - 文档上传
   - 图片上传

2. **批量数据导入**
   - Excel数据导入
   - CSV数据导入
   - 批量用户导入

3. **模板下载功能**
   - 导入模板下载
   - 示例文件下载
   - 格式说明文档下载

## 注意事项

1. `extensionNameList_in` 是必需属性，用于文件类型验证
2. 使用触发器模式控制对话框显示，需要正确设置时间戳
3. 文件上传是异步操作，需要正确处理成功和失败情况
4. 建议在上传前进行文件大小和格式验证
5. 模板下载功能是可选的，可以通过 `hideDownload` 属性控制
6. 权限控制可以通过 `downloadPermission` 属性实现

## 相关组件

- [CetDialog](./CetDialog.md) - 对话框组件
- [CetButton](./CetButton.md) - 按钮组件
