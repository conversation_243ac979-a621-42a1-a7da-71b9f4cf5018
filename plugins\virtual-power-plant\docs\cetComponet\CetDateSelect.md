# CetDateSelect - 日期选择组件

## 组件概述

`CetDateSelect` 是一个功能强大的日期选择组件，支持日、周、月、季、年选择，支持日期范围和日期时间范围选择，内置前后切换按钮和快捷日期选项。

## 组件名称

- `CetDateSelect`

## 基本用法

```vue
<template>
  <div>
    <CetDateSelect
      v-model="dateValue"
      :typeList="['day', 'week', 'month', 'season', 'year']"
      layout="button"
      :showOption="true"
      :showButton="true"
      :align="'left'"
      @dateChange="handleDateChange"
      @dateType_out="handleDateTypeChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      dateValue: {
        type: 'day',
        value: new Date()
      }
    };
  },
  methods: {
    handleDateChange(dateObj) {
      console.log('日期变化:', dateObj);
    },
    handleDateTypeChange(type) {
      console.log('日期类型变化:', type);
    }
  }
};
</script>
```

## 属性说明

### 核心属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | Object | - | 日期值对象（支持 v-model） |
| typeList | Array | ['day', 'week', 'month', 'season', 'year', 'daterange'] | 支持的日期类型列表 |
| layout | String | 'select' | 布局模式（select/button） |

### 显示控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showOption | Boolean | true | 是否显示类型选择器 |
| showButton | Boolean | - | 是否显示前后切换按钮 |
| align | String | 'left' | 对齐方式 |

### 按钮控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| nextBtnDisabled | Boolean | true | 下一个按钮是否禁用 |
| nextDisabledNum | Number | 0 | 禁用的下一个数量 |
| isClosedEnd | Boolean | - | 是否闭合结束（从全局配置获取） |

### 日期选择器配置

| 属性名 | 类型 | 说明 |
|--------|------|------|
| dayDateOption | Object | 日选择器配置 |
| weekDateOption | Object | 周选择器配置 |
| monthDateOption | Object | 月选择器配置 |
| yearDateOptions | Object | 年选择器配置 |
| rangeDateOptions | Object | 范围选择器配置（包含快捷选项） |

## 日期类型说明

| 类型 | 说明 | 返回值格式 |
|------|------|-----------|
| day | 日选择 | { type: 'day', value: Date } |
| week | 周选择 | { type: 'week', value: [startDate, endDate] } |
| month | 月选择 | { type: 'month', value: Date } |
| season | 季度选择 | { type: 'season', value: [startDate, endDate] } |
| year | 年选择 | { type: 'year', value: Date } |
| daterange | 日期范围选择 | { type: 'daterange', value: [startDate, endDate] } |
| datetimerange | 日期时间范围选择 | { type: 'datetimerange', value: [startDate, endDate] } |

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| dateChange | dateObj | 日期变化事件 |
| dateType_out | type | 日期类型变化事件 |

## 使用示例

### 基础日期选择

```vue
<template>
  <div>
    <CetDateSelect
      v-model="basicDate"
      :typeList="['day', 'month', 'year']"
      @dateChange="handleBasicChange"
    />
    
    <p>选中的日期: {{ formatDate(basicDate) }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      basicDate: {
        type: 'day',
        value: new Date()
      }
    };
  },
  methods: {
    handleBasicChange(dateObj) {
      console.log('基础日期变化:', dateObj);
    },
    formatDate(dateObj) {
      if (!dateObj || !dateObj.value) return '';
      
      if (Array.isArray(dateObj.value)) {
        return dateObj.value.map(d => d.toLocaleDateString()).join(' - ');
      }
      return dateObj.value.toLocaleDateString();
    }
  }
};
</script>
```

### 按钮布局模式

```vue
<template>
  <div>
    <CetDateSelect
      v-model="buttonDate"
      :typeList="['day', 'week', 'month']"
      layout="button"
      :showButton="true"
      @dateChange="handleButtonChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      buttonDate: {
        type: 'day',
        value: new Date()
      }
    };
  },
  methods: {
    handleButtonChange(dateObj) {
      console.log('按钮模式日期变化:', dateObj);
    }
  }
};
</script>
```

### 统计报表时间筛选

```vue
<template>
  <div>
    <div class="date-filter">
      <CetDateSelect
        v-model="reportDate"
        :typeList="['day', 'week', 'month', 'season', 'year']"
        layout="select"
        :showButton="true"
        @dateChange="loadReportData"
        @dateType_out="handleTypeChange"
      />
    </div>
    
    <div class="report-content">
      <h3>{{ reportTitle }}</h3>
      <CetChart :options="chartOptions" :inputData_in="reportData" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      reportDate: {
        type: 'month',
        value: new Date()
      },
      reportData: [],
      chartOptions: {
        // 图表配置
      }
    };
  },
  computed: {
    reportTitle() {
      const typeMap = {
        day: '日报',
        week: '周报',
        month: '月报',
        season: '季报',
        year: '年报'
      };
      return typeMap[this.reportDate.type] || '报表';
    }
  },
  methods: {
    loadReportData(dateObj) {
      console.log('加载报表数据:', dateObj);
      // 根据日期类型和值加载对应的报表数据
      this.fetchReportData(dateObj.type, dateObj.value);
    },
    handleTypeChange(type) {
      console.log('报表类型变化:', type);
      // 可以在这里做一些类型切换的处理
    },
    async fetchReportData(type, value) {
      try {
        // 模拟API调用
        const response = await this.$api.getReportData({
          type,
          date: value
        });
        this.reportData = response.data;
      } catch (error) {
        console.error('加载报表数据失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.date-filter {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.report-content {
  padding: 20px;
}
</style>
```

### 日期范围选择

```vue
<template>
  <div>
    <CetDateSelect
      v-model="rangeDate"
      :typeList="['daterange', 'datetimerange']"
      :showButton="false"
      @dateChange="handleRangeChange"
    />
    
    <div v-if="rangeDate.value" class="range-info">
      <p>开始时间: {{ formatDateTime(rangeDate.value[0]) }}</p>
      <p>结束时间: {{ formatDateTime(rangeDate.value[1]) }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      rangeDate: {
        type: 'daterange',
        value: null
      }
    };
  },
  methods: {
    handleRangeChange(dateObj) {
      console.log('范围日期变化:', dateObj);
      if (dateObj.value && dateObj.value.length === 2) {
        this.queryDataByRange(dateObj.value[0], dateObj.value[1]);
      }
    },
    formatDateTime(date) {
      if (!date) return '';
      return date.toLocaleString();
    },
    queryDataByRange(startDate, endDate) {
      console.log('查询范围数据:', startDate, endDate);
      // 执行范围查询逻辑
    }
  }
};
</script>

<style scoped>
.range-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
}
</style>
```

### 禁用未来日期

```vue
<template>
  <div>
    <CetDateSelect
      v-model="historyDate"
      :typeList="['day', 'week', 'month']"
      :nextBtnDisabled="true"
      :nextDisabledNum="0"
      @dateChange="handleHistoryChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      historyDate: {
        type: 'day',
        value: new Date()
      }
    };
  },
  methods: {
    handleHistoryChange(dateObj) {
      console.log('历史日期变化:', dateObj);
      // 只能选择历史日期的逻辑
    }
  }
};
</script>
```

## 主要功能

1. **支持多种时间粒度选择**
   - 日、周、月、季、年选择
   - 日期范围和日期时间范围选择

2. **支持按钮和下拉两种布局模式**
   - select: 下拉选择模式
   - button: 按钮组模式

3. **内置前后切换按钮**
   - 快速切换到前一个/后一个时间段
   - 支持禁用未来时间

4. **内置快捷日期选项**
   - 范围选择时提供快捷选项
   - 可自定义快捷选项

5. **支持禁用未来日期**
   - 通过配置控制是否可选择未来日期
   - 适用于历史数据查询场景

## 适用场景

1. **时间范围选择**
   - 数据查询时间条件
   - 报表时间筛选
   - 统计分析时间范围

2. **统计报表时间筛选**
   - 日报、周报、月报切换
   - 时间维度分析
   - 趋势对比

3. **多种时间粒度选择**
   - 灵活的时间维度选择
   - 不同精度的时间查询
   - 时间段对比分析

4. **数据查询时间条件**
   - 列表筛选条件
   - 搜索时间范围
   - 数据导出时间范围

## 注意事项

1. `typeList` 属性控制可选择的日期类型
2. 不同日期类型返回的数据格式不同，需要正确处理
3. 使用范围选择时，返回值是数组格式
4. `nextBtnDisabled` 可以控制是否禁用未来日期选择
5. 组件会根据全局配置自动调整某些行为
6. 建议为不同的使用场景配置合适的日期类型列表

## 相关组件

- [CustomElDatePicker](./CustomElDatePicker.md) - 自定义日期选择器
- [CetTable](./CetTable.md) - 表格组件
- [CetChart](./CetChart.md) - 图表组件
