# CetTree - 树形组件

## 组件概述

`CetTree` 是基于 ElementUI Tree 组件的增强版本，支持搜索过滤、节点选择和勾选、懒加载、自定义过滤逻辑等功能。

## 组件名称

- `CetTree`

## 基本用法

```vue
<template>
  <div>
    <CetTree
      :inputData_in="treeData"
      :selectNode="selectedNode"
      :checkedNodes="checkedNodes"
      :showFilter="true"
      :showFilterChildNode="false"
      :searchText_in="searchText"
      :view="{ showLine: false }"
      :expandWhenChecked="true"
      @currentChange="handleNodeClick"
      @check="handleNodeCheck"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: [
        {
          id: 1,
          label: '一级节点1',
          children: [
            {
              id: 11,
              label: '二级节点1-1',
              children: [
                { id: 111, label: '三级节点1-1-1' }
              ]
            }
          ]
        },
        {
          id: 2,
          label: '一级节点2',
          children: [
            { id: 21, label: '二级节点2-1' },
            { id: 22, label: '二级节点2-2' }
          ]
        }
      ],
      selectedNode: null,
      checkedNodes: [],
      searchText: ''
    };
  },
  methods: {
    handleNodeClick(node) {
      console.log('节点点击:', node);
      this.selectedNode = node;
    },
    handleNodeCheck(checkedNodes) {
      console.log('节点勾选:', checkedNodes);
      this.checkedNodes = checkedNodes;
    }
  }
};
</script>
```

## 属性说明

### 核心属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| inputData_in | Array | [] | 树形数据 |
| selectNode | Object | null | 当前选中节点 |
| checkedNodes | Array | [] | 已勾选节点数组 |

### 显示控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showFilter | Boolean | false | 是否显示搜索框 |
| showFilterChildNode | Boolean | false | 过滤时是否显示子节点 |
| ShowRootNode | Boolean | - | 是否显示根节点 |
| view | Object | { showLine: false } | 视图配置 |

### 搜索和过滤属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| searchText_in | String | '' | 搜索关键字 |
| filterNodes_in | String/Array | - | 指定过滤的节点 |
| filterNodesKey | String | 'filterNodesKey' | 过滤节点的键名 |

### 根节点配置

| 属性名 | 类型 | 说明 |
|--------|------|------|
| rootName | Object | 根节点配置 |

### 行为控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| expandWhenChecked | Boolean | true | 勾选时是否展开 |
| defaultExpandedKeys | Array | [] | 默认展开的节点键值 |

### 懒加载配置

| 属性名 | 类型 | 说明 |
|--------|------|------|
| nodeModelList | Array | 节点模型列表（用于懒加载） |
| loadFunc | String | 懒加载函数名 |

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| currentChange | node | 当前节点变化事件 |
| check | checkedNodes | 节点勾选事件 |

## 方法说明

继承 ElementUI Tree 的所有方法：

| 方法名 | 参数 | 说明 |
|--------|------|------|
| filter | value | 过滤节点 |
| getCheckedNodes | leafOnly, includeHalfChecked | 获取勾选节点 |
| setCheckedNodes | nodes | 设置勾选节点 |
| getCheckedKeys | leafOnly | 获取勾选节点的key |
| setCheckedKeys | keys, leafOnly | 设置勾选节点的key |
| setChecked | data, checked, deep | 设置节点勾选状态 |

## 使用示例

### 基础树形结构

```vue
<template>
  <div>
    <CetTree
      :inputData_in="departmentTree"
      :showFilter="true"
      @currentChange="handleDepartmentSelect"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      departmentTree: [
        {
          id: 1,
          label: '总公司',
          children: [
            {
              id: 11,
              label: '技术部',
              children: [
                { id: 111, label: '前端组' },
                { id: 112, label: '后端组' }
              ]
            },
            {
              id: 12,
              label: '市场部',
              children: [
                { id: 121, label: '销售组' },
                { id: 122, label: '推广组' }
              ]
            }
          ]
        }
      ]
    };
  },
  methods: {
    handleDepartmentSelect(node) {
      console.log('选中部门:', node);
      // 加载部门下的员工信息
      this.loadEmployees(node.id);
    },
    loadEmployees(departmentId) {
      // 加载员工逻辑
    }
  }
};
</script>
```

### 带搜索的树形组件

```vue
<template>
  <div>
    <div class="tree-search">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索节点"
        prefix-icon="el-icon-search"
        @input="handleSearch"
      />
    </div>
    
    <CetTree
      :inputData_in="filteredTreeData"
      :searchText_in="searchKeyword"
      :showFilter="true"
      :showFilterChildNode="true"
      @currentChange="handleNodeSelect"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      originalTreeData: [
        {
          id: 1,
          label: '系统管理',
          children: [
            { id: 11, label: '用户管理' },
            { id: 12, label: '角色管理' },
            { id: 13, label: '权限管理' }
          ]
        },
        {
          id: 2,
          label: '业务管理',
          children: [
            { id: 21, label: '订单管理' },
            { id: 22, label: '商品管理' }
          ]
        }
      ]
    };
  },
  computed: {
    filteredTreeData() {
      if (!this.searchKeyword) {
        return this.originalTreeData;
      }
      return this.filterTreeData(this.originalTreeData, this.searchKeyword);
    }
  },
  methods: {
    handleSearch(keyword) {
      console.log('搜索关键字:', keyword);
    },
    handleNodeSelect(node) {
      console.log('选中节点:', node);
    },
    filterTreeData(data, keyword) {
      return data.filter(item => {
        if (item.label.includes(keyword)) {
          return true;
        }
        if (item.children) {
          item.children = this.filterTreeData(item.children, keyword);
          return item.children.length > 0;
        }
        return false;
      });
    }
  }
};
</script>
```

### 可勾选的树形组件

```vue
<template>
  <div>
    <div class="tree-actions">
      <CetButton title="全选" @statusTrigger_out="checkAll" />
      <CetButton title="全不选" @statusTrigger_out="uncheckAll" />
      <CetButton title="展开全部" @statusTrigger_out="expandAll" />
      <CetButton title="收起全部" @statusTrigger_out="collapseAll" />
    </div>
    
    <CetTree
      :inputData_in="permissionTree"
      :checkedNodes="selectedPermissions"
      :expandWhenChecked="true"
      show-checkbox
      node-key="id"
      ref="permissionTree"
      @check="handlePermissionCheck"
    />
    
    <div class="selected-info">
      <h4>已选择的权限：</h4>
      <ul>
        <li v-for="permission in selectedPermissions" :key="permission.id">
          {{ permission.label }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      permissionTree: [
        {
          id: 1,
          label: '系统管理',
          children: [
            { id: 11, label: '用户管理' },
            { id: 12, label: '角色管理' }
          ]
        },
        {
          id: 2,
          label: '内容管理',
          children: [
            { id: 21, label: '文章管理' },
            { id: 22, label: '分类管理' }
          ]
        }
      ],
      selectedPermissions: []
    };
  },
  methods: {
    handlePermissionCheck(checkedNodes) {
      this.selectedPermissions = checkedNodes;
      console.log('选中的权限:', checkedNodes);
    },
    checkAll() {
      this.$refs.permissionTree.setCheckedNodes(this.getAllNodes(this.permissionTree));
    },
    uncheckAll() {
      this.$refs.permissionTree.setCheckedNodes([]);
    },
    expandAll() {
      // 展开所有节点的逻辑
      this.setExpandAll(true);
    },
    collapseAll() {
      // 收起所有节点的逻辑
      this.setExpandAll(false);
    },
    getAllNodes(data) {
      let nodes = [];
      data.forEach(item => {
        nodes.push(item);
        if (item.children) {
          nodes = nodes.concat(this.getAllNodes(item.children));
        }
      });
      return nodes;
    },
    setExpandAll(expand) {
      const allKeys = this.getAllNodeKeys(this.permissionTree);
      if (expand) {
        this.$refs.permissionTree.setExpandedKeys(allKeys);
      } else {
        this.$refs.permissionTree.setExpandedKeys([]);
      }
    },
    getAllNodeKeys(data) {
      let keys = [];
      data.forEach(item => {
        keys.push(item.id);
        if (item.children) {
          keys = keys.concat(this.getAllNodeKeys(item.children));
        }
      });
      return keys;
    }
  }
};
</script>

<style scoped>
.tree-actions {
  margin-bottom: 10px;
}

.selected-info {
  margin-top: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
```

### 懒加载树形组件

```vue
<template>
  <div>
    <CetTree
      :inputData_in="lazyTreeData"
      :nodeModelList="nodeModels"
      :loadFunc="loadChildNodes"
      lazy
      :load="loadNode"
      :props="treeProps"
      @currentChange="handleLazyNodeSelect"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      lazyTreeData: [],
      nodeModels: [],
      treeProps: {
        label: 'name',
        children: 'children',
        isLeaf: 'leaf'
      }
    };
  },
  methods: {
    loadNode(node, resolve) {
      if (node.level === 0) {
        // 加载根节点
        setTimeout(() => {
          resolve([
            { name: '根节点1', leaf: false },
            { name: '根节点2', leaf: false }
          ]);
        }, 500);
      } else {
        // 加载子节点
        setTimeout(() => {
          resolve([
            { name: `${node.data.name}-子节点1`, leaf: true },
            { name: `${node.data.name}-子节点2`, leaf: true }
          ]);
        }, 500);
      }
    },
    loadChildNodes(node) {
      // 自定义懒加载逻辑
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            { id: Date.now(), name: '动态加载的节点', leaf: true }
          ]);
        }, 1000);
      });
    },
    handleLazyNodeSelect(node) {
      console.log('懒加载节点选择:', node);
    }
  }
};
</script>
```

### 自定义节点内容

```vue
<template>
  <div>
    <CetTree
      :inputData_in="customTreeData"
      @currentChange="handleCustomNodeSelect"
    >
      <template slot-scope="{ node, data }">
        <div class="custom-tree-node">
          <i :class="getNodeIcon(data)"></i>
          <span>{{ data.label }}</span>
          <span class="node-actions">
            <el-button
              type="text"
              size="mini"
              @click.stop="editNode(data)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="deleteNode(node, data)"
            >
              删除
            </el-button>
          </span>
        </div>
      </template>
    </CetTree>
  </div>
</template>

<script>
export default {
  data() {
    return {
      customTreeData: [
        {
          id: 1,
          label: '文件夹1',
          type: 'folder',
          children: [
            { id: 11, label: '文档1.txt', type: 'file' },
            { id: 12, label: '图片1.jpg', type: 'image' }
          ]
        },
        {
          id: 2,
          label: '文件夹2',
          type: 'folder',
          children: [
            { id: 21, label: '视频1.mp4', type: 'video' }
          ]
        }
      ]
    };
  },
  methods: {
    getNodeIcon(data) {
      const iconMap = {
        folder: 'el-icon-folder',
        file: 'el-icon-document',
        image: 'el-icon-picture',
        video: 'el-icon-video-camera'
      };
      return iconMap[data.type] || 'el-icon-document';
    },
    handleCustomNodeSelect(node) {
      console.log('自定义节点选择:', node);
    },
    editNode(data) {
      console.log('编辑节点:', data);
      // 编辑节点逻辑
    },
    deleteNode(node, data) {
      console.log('删除节点:', data);
      // 删除节点逻辑
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
    }
  }
};
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.custom-tree-node:hover .node-actions {
  opacity: 1;
}
</style>
```

## 主要功能

1. **支持搜索过滤**
   - 内置搜索框
   - 自定义过滤逻辑
   - 高亮匹配结果

2. **支持节点选择和勾选**
   - 单选模式
   - 多选模式
   - 父子关联选择

3. **支持懒加载**
   - 异步加载子节点
   - 自定义加载函数
   - 加载状态指示

4. **自定义过滤逻辑**
   - 自定义过滤函数
   - 多条件过滤
   - 动态过滤

5. **继承 ElementUI Tree 的功能**
   - 完整的树形功能
   - 自定义节点模板
   - 拖拽排序

## 适用场景

1. **层级数据展示**
   - 组织架构
   - 文件目录
   - 分类管理

2. **组织架构树**
   - 部门管理
   - 人员组织
   - 层级关系

3. **分类选择器**
   - 商品分类
   - 内容分类
   - 标签分类

4. **权限树管理**
   - 权限分配
   - 角色权限
   - 功能权限

## 注意事项

1. 树形数据结构要符合组件要求
2. 使用懒加载时要正确处理异步逻辑
3. 搜索过滤时注意性能优化
4. 自定义节点模板时要处理好事件冒泡
5. 大数据量时建议使用懒加载或虚拟滚动
6. 节点操作时要正确更新数据结构

## 相关组件

- [CetSelectTree](./CetSelectTree.md) - 树形选择器
- [CetGiantTree](./CetGiantTree.md) - 大数据量树形组件
- [CetZtree](./CetZtree.md) - 基于zTree的树形组件
