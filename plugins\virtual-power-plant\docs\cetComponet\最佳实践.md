# 最佳实践

## 概述

本文档提供了使用三个组件库的最佳实践、使用建议和注意事项，帮助开发者更好地使用组件库。

## 命名规范

### 组件命名
- **组件名使用 PascalCase**
  ```vue
  <CetButton />
  <CetDialog />
  <CustomElSelect />
  ```

- **组件文件命名使用 PascalCase**
  ```
  components/
    ├── UserManagement.vue
    ├── DataTable.vue
    └── ReportChart.vue
  ```

### 属性和事件命名
- **属性名使用 camelCase 或 snake_case（根据组件要求）**
  ```vue
  <!-- camelCase -->
  <CetButton :disableIn="false" />
  
  <!-- snake_case -->
  <CustomElSelect :prefix_in="'选择类型'" />
  ```

- **事件名使用 camelCase**
  ```vue
  <CetButton @statusTrigger_out="handleClick" />
  <CetTable @currentChange_out="handleRowChange" />
  ```

### 变量命名
```javascript
// 数据变量使用 camelCase
data() {
  return {
    tableData: [],
    selectedRows: [],
    formData: {},
    openTrigger: 0
  };
}
```

## 组件使用建议

### 1. 优先使用组件库提供的组件

**推荐做法：**
```vue
<!-- 使用组件库组件保持界面一致性 -->
<CetButton title="确定" type="primary" @statusTrigger_out="handleConfirm" />
<CetDialog :openTrigger_in="openTrigger" title="用户信息">
  <CetForm :data="formData" />
</CetDialog>
```

**避免做法：**
```vue
<!-- 避免混用原生ElementUI组件 -->
<el-button @click="handleConfirm">确定</el-button>
<el-dialog :visible.sync="dialogVisible">
  <el-form :model="formData" />
</el-dialog>
```

### 2. 合理使用触发器模式进行组件间通信

**推荐做法：**
```vue
<template>
  <div>
    <CetButton title="查询" @statusTrigger_out="triggerQuery" />
    <CetButton title="刷新" @statusTrigger_out="triggerRefresh" />
    
    <CetTable 
      :queryTrigger_in="queryTrigger"
      :refreshTrigger_in="refreshTrigger"
      :data="tableData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryTrigger: 0,
      refreshTrigger: 0,
      tableData: []
    };
  },
  methods: {
    triggerQuery(timestamp) {
      this.queryTrigger = timestamp;
    },
    triggerRefresh(timestamp) {
      this.refreshTrigger = timestamp;
    }
  }
};
</script>
```

### 3. 充分利用组件的插槽功能进行自定义

**推荐做法：**
```vue
<CetDialog :openTrigger_in="openTrigger" title="用户详情">
  <!-- 自定义标题 -->
  <template #title>
    <div class="custom-title">
      <i class="el-icon-user"></i>
      <span>用户详情</span>
    </div>
  </template>
  
  <!-- 主要内容 -->
  <div class="user-detail">
    <p>姓名: {{ user.name }}</p>
    <p>邮箱: {{ user.email }}</p>
  </div>
  
  <!-- 自定义底部 -->
  <template #footer>
    <CetButton title="编辑" type="primary" @statusTrigger_out="editUser" />
    <CetButton title="关闭" @statusTrigger_out="closeDialog" />
  </template>
</CetDialog>
```

### 4. 注意组件的生命周期和数据绑定

**推荐做法：**
```vue
<template>
  <CetTabs v-model="activeTab">
    <UserList label="用户列表" name="users" />
    <UserDetail label="用户详情" name="detail" />
  </CetTabs>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'users'
    };
  },
  // 利用组件生命周期
  activated() {
    // 组件激活时刷新数据
    this.refreshData();
  },
  deactivated() {
    // 组件失活时保存状态
    this.saveState();
  }
};
</script>
```

## 数据绑定最佳实践

### 1. 深度监听和浅度监听的区别

```javascript
export default {
  data() {
    return {
      tableData: [],
      formData: {}
    };
  },
  watch: {
    // 深度监听对象变化
    formData: {
      handler(newVal, oldVal) {
        console.log('表单数据变化');
      },
      deep: true
    },
    // 浅度监听数组变化
    tableData(newVal, oldVal) {
      console.log('表格数据变化');
    }
  }
};
```

### 2. 正确使用 .sync 修饰符

**推荐做法：**
```vue
<template>
  <CetTable 
    :data.sync="tableData"
    :dynamicInput.sync="queryParams"
    @outputData_out="handleDataOutput"
  />
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      queryParams: {}
    };
  },
  methods: {
    handleDataOutput(data) {
      // 正确处理数据变化
      console.log('表格数据输出:', data);
    }
  }
};
</script>
```

### 3. 异步数据加载时注意组件的生命周期

```javascript
export default {
  data() {
    return {
      loading: false,
      tableData: []
    };
  },
  async created() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      try {
        const response = await this.$api.getData();
        this.tableData = response.data;
      } catch (error) {
        this.$message.error('数据加载失败');
      } finally {
        this.loading = false;
      }
    }
  }
};
```

## 性能优化建议

### 1. 表格和树组件的大数据量处理

**使用分页：**
```vue
<CetTable 
  :data="tableData"
  :showPagination="true"
  :paginationCfg="paginationConfig"
  @outputData_out="handlePageChange"
/>
```

**使用懒加载：**
```vue
<CetTree 
  :inputData_in="treeData"
  :nodeModelList="nodeModels"
  :loadFunc="loadChildNodes"
/>
```

### 2. 图表组件性能优化

```vue
<template>
  <CetChart 
    :options="chartOptions"
    :inputData_in="chartData"
    :watchShallow="true"
    :manualUpdate="true"
    ref="chart"
  />
</template>

<script>
export default {
  methods: {
    updateChart() {
      // 手动更新图表
      this.$refs.chart.mergeOptions(newOptions);
    }
  }
};
</script>
```

### 3. 组件懒加载

```javascript
// 路由懒加载
const UserManagement = () => import('@/views/UserManagement.vue');

// 组件懒加载
export default {
  components: {
    CetTable: () => import('cet-common').then(m => m.CetTable)
  }
};
```

## 错误处理和调试

### 1. 错误边界处理

```vue
<template>
  <div>
    <CetTable 
      :data="tableData"
      @error="handleTableError"
    />
  </div>
</template>

<script>
export default {
  methods: {
    handleTableError(error) {
      console.error('表格错误:', error);
      this.$message.error('表格加载失败，请重试');
    }
  }
};
</script>
```

### 2. 调试技巧

```javascript
export default {
  data() {
    return {
      debugMode: process.env.NODE_ENV === 'development'
    };
  },
  methods: {
    handleClick(timestamp) {
      if (this.debugMode) {
        console.log('按钮点击时间戳:', timestamp);
        console.log('当前组件状态:', this.$data);
      }
      // 业务逻辑
    }
  }
};
```

## 样式和主题

### 1. 样式覆盖

```scss
// 使用深度选择器覆盖组件样式
.my-component {
  ::v-deep .cet-button {
    border-radius: 8px;
  }
  
  ::v-deep .cet-table {
    .el-table__header {
      background-color: #f5f5f5;
    }
  }
}
```

### 2. 主题定制

```javascript
// 注册自定义主题
import { registerTheme } from "cet-chart";

registerTheme('company', {
  color: ['#1890ff', '#52c41a', '#faad14', '#f5222d'],
  backgroundColor: '#fff',
  textStyle: {
    color: '#333',
    fontFamily: 'PingFang SC, Microsoft YaHei'
  }
});
```

## 测试建议

### 1. 单元测试

```javascript
import { shallowMount } from '@vue/test-utils';
import CetButton from 'cet-common';

describe('CetButton', () => {
  it('should emit statusTrigger_out when clicked', () => {
    const wrapper = shallowMount(CetButton, {
      propsData: {
        title: 'Test Button'
      }
    });
    
    wrapper.trigger('click');
    expect(wrapper.emitted('statusTrigger_out')).toBeTruthy();
  });
});
```

### 2. 集成测试

```javascript
describe('UserManagement', () => {
  it('should load user data on mount', async () => {
    const wrapper = mount(UserManagement);
    await wrapper.vm.$nextTick();
    
    expect(wrapper.find('CetTable').props('data')).toHaveLength(10);
  });
});
```

## 常见问题和解决方案

### 1. 触发器不生效

**问题：** 触发器属性设置后组件没有响应

**解决方案：**
```javascript
// 确保使用时间戳
methods: {
  triggerAction() {
    this.actionTrigger = Date.now(); // 使用时间戳
    // 不要使用 this.actionTrigger = true;
  }
}
```

### 2. 表格数据不更新

**问题：** 修改数据后表格不刷新

**解决方案：**
```javascript
// 使用 Vue.set 或数组方法
methods: {
  updateTableData() {
    // 推荐做法
    this.tableData = [...newData];
    
    // 或者使用 Vue.set
    this.$set(this, 'tableData', newData);
  }
}
```

### 3. 图表不显示

**问题：** 图表组件渲染后不显示

**解决方案：**
```css
/* 确保容器有明确的宽高 */
.chart-container {
  width: 100%;
  height: 400px;
}
```

## 版本升级指南

### 1. 升级前准备

- 备份当前代码
- 查看版本更新日志
- 测试关键功能

### 2. 升级步骤

```bash
# 更新组件库版本
npm update eem-base cet-common cet-chart

# 检查依赖冲突
npm audit

# 运行测试
npm run test
```

### 3. 升级后验证

- 检查组件API变化
- 验证样式是否正常
- 测试核心功能

## 相关文档

- [README](./README.md) - 组件库总体介绍
- [全局配置](./全局配置.md) - 详细的全局配置说明
