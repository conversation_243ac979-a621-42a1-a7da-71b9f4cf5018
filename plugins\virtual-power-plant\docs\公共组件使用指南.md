# 公共组件使用指南

本文档详细介绍了项目中使用的三个核心组件库：`eem-base`、`cet-common` 和 `cet-chart` 的使用规范和适用范围。

## 一、EEM-BASE 组件库

### 1.1 CustomElSelect (自定义选择器)

**组件名称：** `CustomElSelect` 或 `customElSelect`

**使用规范：**
```vue
<CustomElSelect 
  prefix_in="选择类型" 
  popover_in="这是一个提示信息"
  v-model="selectedValue"
>
  <el-option label="选项1" value="1"></el-option>
  <el-option label="选项2" value="2"></el-option>
</CustomElSelect>
```

**属性说明：**
- `prefix_in`: String - 前缀标签文本
- `popover_in`: String - 悬停提示内容（可选）
- 继承 ElementUI Select 的所有属性和事件

**适用场景：**
- 需要带前缀标签的下拉选择器
- 需要问号提示图标的选择器
- 自定义样式的选择器（带边框、圆角等）

### 1.2 CustomElDatePicker (自定义日期选择器)

**组件名称：** `CustomElDatePicker`

**使用规范：**
```vue
<CustomElDatePicker 
  prefix_in="选择日期" 
  v-model="dateValue"
  type="date"
/>
```

**属性说明：**
- `prefix_in`: String - 前缀标签文本
- 继承 ElementUI DatePicker 的所有属性和事件

**适用场景：**
- 需要带前缀标签的日期选择器
- 自定义样式的日期选择器

### 1.3 CustomSteps (自定义步骤条)

**组件名称：** `customSteps`

**使用规范：**
```vue
<customSteps 
  :active="currentStep" 
  :steps="stepList"
/>
```

**属性说明：**
- `steps`: Array - 步骤数据（必需）
  - 每项包含：`title` (必需), `description` (可选)
- `active`: Number - 当前激活步骤索引

**数据格式：**
```javascript
stepList: [
  { title: '第一步', description: '填写基本信息' },
  { title: '第二步', description: '确认信息' },
  { title: '第三步', description: '完成' }
]
```

**适用场景：**
- 多步骤流程展示
- 向导式操作界面
- 进度指示器

### 1.4 UploadDialog (上传对话框)

**组件名称：** `UploadDialog`

**使用规范：**
```vue
<UploadDialog
  :openTrigger_in="openTrigger"
  :closeTrigger_in="closeTrigger"
  :extensionNameList_in="['.xlsx', '.xls']"
  dialogTitle="批量导入"
  :maxFlinkCount="1000"
  @uploadFile="handleUpload"
  @download="handleDownload"
/>
```

**属性说明：**
- `extensionNameList_in`: Array - 支持的文件扩展名列表（必需）
- `openTrigger_in`: Number - 打开触发器
- `closeTrigger_in`: Number - 关闭触发器
- `hideDownload`: Boolean - 是否隐藏下载模板按钮
- `dialogTitle`: String - 对话框标题
- `downloadPermission`: String - 下载权限
- `maxFlinkCount`: Number - 最大导入数据量

**事件说明：**
- `@uploadFile`: 文件上传事件
- `@download`: 模板下载事件

**适用场景：**
- 文件上传功能
- 批量数据导入
- 模板下载功能



## 二、CET-COMMON 组件库

### 2.1 CetButton (按钮组件)

**组件名称：** `CetButton`

**使用规范：**
```vue
<CetButton
  :visible_in="true"
  :disable_in="false"
  title="确定"
  size="small"
  type="primary"
  icon="el-icon-check"
  @statusTrigger_out="handleClick"
/>
```

**属性说明：**
- `visible_in`: Boolean - 是否显示按钮
- `disable_in`: Boolean - 是否禁用按钮
- `title`: String - 按钮显示文本
- `size`: String - 按钮尺寸，默认 'small'
  - 可选值：'medium'、'small'、'mini'
- 继承 ElementUI Button 的所有属性：
  - `type`: String - 按钮类型（primary/success/warning/danger/info/text）
  - `plain`: Boolean - 是否朴素按钮
  - `round`: Boolean - 是否圆角按钮
  - `circle`: Boolean - 是否圆形按钮
  - `loading`: Boolean - 是否加载中状态
  - `icon`: String - 图标类名
  - `autofocus`: Boolean - 是否默认聚焦

**事件说明：**
- `@statusTrigger_out`: 点击时触发，返回当前时间戳
- 继承 ElementUI Button 的所有事件：
  - `@click`: 点击事件
  - `@focus`: 获得焦点事件
  - `@blur`: 失去焦点事件

**主要功能：**
- 基于 ElementUI Button 的增强组件
- 提供显示/隐藏控制
- 提供禁用状态控制
- 点击时自动生成时间戳

**适用场景：**
- 统一样式的操作按钮
- 需要状态控制的按钮
- 表单提交按钮
- 触发器模式的按钮

### 2.2 CetDialog (对话框组件)

**组件名称：** `CetDialog`

**使用规范：**
```vue
<CetDialog
  :openTrigger_in="openTrigger"
  :closeTrigger_in="closeTrigger"
  title="对话框标题"
  width="800px"
  :closeOnClickModal="false"
  :closeOnPressEscape="false"
  :showClose="true"
  :isDraggable="false"
  @openTrigger_out="handleOpen"
  @closeTrigger_out="handleClose"
>
  <div>对话框内容</div>
  <template #title>
    <span>自定义标题</span>
  </template>
  <template #footer>
    <CetButton title="取消" @statusTrigger_out="handleCancel"/>
    <CetButton title="确定" type="primary" @statusTrigger_out="handleConfirm"/>
  </template>
</CetDialog>
```

**属性说明：**
- `openTrigger_in`: Number - 打开对话框触发器（时间戳）
- `closeTrigger_in`: Number - 关闭对话框触发器（时间戳）
- `title`: String - 对话框标题，默认 '弹窗'
- `width`: String - 对话框宽度，默认 '960px'
- `closeOnClickModal`: Boolean - 是否可以通过点击遮罩关闭，默认 false
- `closeOnPressEscape`: Boolean - 是否可以通过按下 ESC 关闭，默认 false
- `showClose`: Boolean - 是否显示关闭按钮，默认 false
- `isDraggable`: Boolean - 是否可拖拽，默认从全局配置获取
- 继承 ElementUI Dialog 的其他属性：
  - `modal`: Boolean - 是否需要遮罩层
  - `modalAppendToBody`: Boolean - 遮罩层是否插入至 body 元素上
  - `appendToBody`: Boolean - 是否插入至 body 元素上
  - `lockScroll`: Boolean - 是否在弹出时将 body 滚动锁定
  - `customClass`: String - 自定义类名
  - `center`: Boolean - 是否对头部和底部采用居中布局

**插槽说明：**
- `default`: 默认插槽，对话框内容
- `title`: 标题插槽
- `footer`: 底部插槽

**事件说明：**
- `@openTrigger_out`: 对话框打开后触发，返回时间戳
- `@closeTrigger_out`: 对话框关闭后触发，返回时间戳
- 继承 ElementUI Dialog 的所有事件：
  - `@open`: 打开事件
  - `@opened`: 打开动画结束事件
  - `@close`: 关闭事件
  - `@closed`: 关闭动画结束事件

**主要功能：**
- 基于 ElementUI Dialog 的增强组件
- 支持触发器模式控制显示/隐藏
- 支持拖拽功能（通过指令实现）
- 提供标准的插槽布局

**适用场景：**
- 模态对话框
- 弹窗表单
- 信息展示窗口
- 确认操作对话框

### 2.3 CetTable (表格组件)

**组件名称：** `CetTable`

**使用规范：**
```vue
<CetTable
  :data.sync="tableData"
  :dynamicInput.sync="dynamicInput"
  queryMode="trigger"
  dataMode="component"
  :dataConfig="dataConfig"
  :showPagination="true"
  :border="true"
  :highlightCurrentRow="true"
  @outputData_out="handleDataOutput"
  @currentChange_out="handleCurrentChange"
>
  <el-table-column prop="name" label="姓名"></el-table-column>
  <el-table-column prop="age" label="年龄"></el-table-column>
</CetTable>
```

**属性说明：**
- `data`: Array - 表格数据（支持 .sync）
- `dynamicInput`: Object - 动态查询条件（支持 .sync）
- `queryMode`: String - 查询模式（trigger/diff）
- `dataMode`: String - 数据获取模式（backendInterface/component/static）
- `dataConfig`: Object - 数据绑定配置
- `showPagination`: Boolean - 是否显示分页器
- `showPaginationUI`: Boolean - 是否显示分页UI，默认 true
- `border`: Boolean - 是否显示边框，默认 true
- `highlightCurrentRow`: Boolean - 是否高亮当前行，默认 true
- `isTreeData`: Boolean - 是否为树形数据，默认 false
- `tableKey`: String - 表格主键字段，默认 'id'
- `tooltipEffect`: String - tooltip 效果
- `paginationCfg`: Object - 分页器配置
- `exportFileName`: String/Function - 导出文件名
- `exportMultiHeader`: Function - 多级表头导出配置
- `isColumsHeaderDoLayout`: Boolean - 是否自动调整列宽

**触发器属性：**
- `queryTrigger_in`: Number - 查询触发器
- `deleteTrigger_in`: Number - 删除触发器
- `localDeleteTrigger_in`: Number - 本地删除触发器
- `batchDeletionTrigger_in`: Number - 批量删除触发器
- `localBatchDeletionTrigger_in`: Number - 本地批量删除触发器
- `refreshTrigger_in`: Number - 刷新触发器
- `clearTrigger_in`: Number - 清空触发器
- `exportTrigger_in`: Number - 导出触发器
- `doLayoutTrigger_in`: Number - 重新布局触发器

**数据输入属性：**
- `addData_in`: Object - 新增数据
- `editData_in`: Object - 编辑数据
- `multiSelectData_in`: Array - 设置多选数据
- `selectRowData_in`: Object - 设置选中行数据
- `queryNode_in`: Object - 查询节点输入

**事件说明：**
- `@outputData_out`: 数据输出事件
- `@currentChange_out`: 当前行变化事件
- `@multiSelectData_out`: 多选数据变化事件
- `@deleteData_out`: 删除数据事件
- `@finishTrigger_out`: 操作完成事件

**主要功能：**
- 内置分页器
- 支持排序、筛选
- 支持行选择和多选
- 支持数据导出
- 支持树形数据展示
- 继承 ElementUI Table 的所有功能

**适用场景：**
- 数据列表展示
- 复杂表格操作
- 带分页的数据表格
- 树形数据表格

### 2.4 CetForm (表单组件)

**组件名称：** `CetForm`

**使用规范：**
```vue
<CetForm
  :data="formData"
  dataMode="static"
  queryMode="trigger"
  :dataConfig="dataConfig"
  :rules="validationRules"
  :refreshAfterActived="false"
  @currentData_out="handleDataChange"
  @finishTrigger_out="handleFinish"
>
  <el-form-item label="姓名" prop="name">
    <el-input v-model="formData.name"></el-input>
  </el-form-item>
</CetForm>
```

**属性说明：**
- `data`: Object - 表单数据对象
- `dataMode`: String - 数据获取模式
  - `backendInterface`: 后端接口模式
  - `component`: 组件数据模式
  - `static`: 静态数据模式
- `queryMode`: String - 查询模式
  - `trigger`: 触发器模式（按钮触发）
  - `diff`: 差异模式（数据变化立即触发）
- `dataConfig`: Object - 数据绑定配置
- `rules`: Object - 表单验证规则
- `refreshAfterActived`: Boolean - 激活后是否刷新，默认 false

**输入属性：**
- `inputData_in`: Object - 外部输入数据
- `queryId_in`: Number - 查询数据的ID，默认 -1

**触发器属性：**
- `queryTrigger_in`: Number - 查询触发器
- `saveTrigger_in`: Number - 保存触发器
- `localSaveTrigger_in`: Number - 本地保存触发器
- `resetTrigger_in`: Number - 重置触发器

**事件说明：**
- `@currentData_out`: 当前数据变化事件
- `@finishTrigger_out`: 操作完成事件
- `@saveData_out`: 保存数据事件
- 继承 ElementUI Form 的所有事件

**方法说明：**
- `validate()`: 表单验证
- `validateField()`: 验证指定字段
- `resetFields()`: 重置表单字段
- `clearValidate()`: 清除验证结果

**适用场景：**
- 复杂表单处理
- 数据绑定表单
- 动态表单生成
- 表单验证和提交

### 2.5 CetDateSelect (日期选择组件)

**组件名称：** `CetDateSelect`

**使用规范：**
```vue
<CetDateSelect
  v-model="dateValue"
  :typeList="['day', 'week', 'month', 'season', 'year']"
  layout="button"
  :showOption="true"
  :showButton="true"
  :align="'left'"
  :nextBtnDisabled="true"
  :nextDisabledNum="0"
  @dateChange="handleDateChange"
  @dateType_out="handleDateTypeChange"
/>
```

**属性说明：**
- `value`: Object - 日期值对象（支持 v-model）
- `typeList`: Array - 支持的日期类型列表，默认 ['day', 'week', 'month', 'season', 'year', 'daterange']
  - `day`: 日选择
  - `week`: 周选择
  - `month`: 月选择
  - `season`: 季度选择
  - `year`: 年选择
  - `daterange`: 日期范围选择
  - `datetimerange`: 日期时间范围选择
- `layout`: String - 布局模式，默认 'select'
  - `select`: 下拉选择模式
  - `button`: 按钮组模式
- `showOption`: Boolean - 是否显示类型选择器，默认 true
- `showButton`: Boolean - 是否显示前后切换按钮
- `align`: String - 对齐方式，默认 'left'
- `nextBtnDisabled`: Boolean - 下一个按钮是否禁用，默认 true
- `nextDisabledNum`: Number - 禁用的下一个数量，默认 0
- `isClosedEnd`: Boolean - 是否闭合结束，默认从全局配置获取

**日期选择器配置：**
- `dayDateOption`: Object - 日选择器配置
- `weekDateOption`: Object - 周选择器配置
- `monthDateOption`: Object - 月选择器配置
- `yearDateOptions`: Object - 年选择器配置
- `rangeDateOptions`: Object - 范围选择器配置（包含快捷选项）

**事件说明：**
- `@dateChange`: 日期变化事件
- `@dateType_out`: 日期类型变化事件
- 继承 ElementUI DatePicker 的所有事件

**主要功能：**
- 支持日、周、月、季、年选择
- 支持日期范围和日期时间范围选择
- 支持按钮和下拉两种布局模式
- 内置前后切换按钮
- 内置快捷日期选项
- 支持禁用未来日期

**适用场景：**
- 时间范围选择
- 统计报表时间筛选
- 多种时间粒度选择
- 数据查询时间条件

### 2.6 CetTree (树形组件)

**组件名称：** `CetTree`

**使用规范：**
```vue
<CetTree
  :inputData_in="treeData"
  :selectNode="selectedNode"
  :checkedNodes="checkedNodes"
  :showFilter="true"
  :showFilterChildNode="false"
  :searchText_in="searchText"
  :filterNodes_in="filterNodes"
  :view="{ showLine: false }"
  :expandWhenChecked="true"
  @currentChange="handleNodeClick"
  @check="handleNodeCheck"
/>
```

**属性说明：**
- `inputData_in`: Array - 树形数据
- `selectNode`: Object - 当前选中节点
- `checkedNodes`: Array - 已勾选节点数组
- `showFilter`: Boolean - 是否显示搜索框
- `showFilterChildNode`: Boolean - 过滤时是否显示子节点，默认 false
- `searchText_in`: String - 搜索关键字
- `filterNodes_in`: String/Array - 指定过滤的节点
- `filterNodesKey`: String - 过滤节点的键名，默认 'filterNodesKey'
- `ShowRootNode`: Boolean - 是否显示根节点
- `rootName`: Object - 根节点配置
- `view`: Object - 视图配置，默认 { showLine: false }
- `expandWhenChecked`: Boolean - 勾选时是否展开，默认 true

**懒加载配置：**
- `nodeModelList`: Array - 节点模型列表（用于懒加载）
- `loadFunc`: String - 懒加载函数名

**展开配置：**
- `defaultExpandedKeys`: Array - 默认展开的节点键值，默认 []

**事件说明：**
- `@currentChange`: 当前节点变化事件
- `@check`: 节点勾选事件
- 继承 ElementUI Tree 的所有事件

**方法说明：**
- 继承 ElementUI Tree 的所有方法
- `filter()`: 过滤节点
- `getCheckedNodes()`: 获取勾选节点
- `setCheckedNodes()`: 设置勾选节点

**主要功能：**
- 支持搜索过滤
- 支持节点选择和勾选
- 支持懒加载
- 支持自定义过滤逻辑
- 继承 ElementUI Tree 的功能

**适用场景：**
- 层级数据展示
- 组织架构树
- 分类选择器
- 权限树管理

### 2.7 CetSelectTree (树形选择器)

**组件名称：** `CetSelectTree`

**使用规范：**
```vue
<CetSelectTree
  v-model="selectedValue"
  :data="treeData"
  :multiple="false"
  :clearable="true"
  :filterable="true"
  :placeholder="'请选择'"
  :props="{ label: 'name', value: 'id', children: 'children' }"
  :node-key="'id'"
  :default-expanded-keys="[]"
  :check-strictly="false"
  @change="handleChange"
  @remove-tag="handleRemoveTag"
/>
```

**属性说明：**
- `value`: String/Number/Array - 选中值（支持 v-model）
- `data`: Array - 树形数据
- `multiple`: Boolean - 是否多选，默认 false
- `clearable`: Boolean - 是否可清空，默认 true
- `filterable`: Boolean - 是否可搜索，默认 true
- `placeholder`: String - 占位符文本
- `props`: Object - 树节点属性配置
  - `label`: 显示字段名
  - `value`: 值字段名
  - `children`: 子节点字段名
- `nodeKey`: String - 节点唯一标识字段
- `defaultExpandedKeys`: Array - 默认展开的节点
- `checkStrictly`: Boolean - 是否严格模式（父子不关联），默认 false

**Select 相关属性：**
- 继承 ElementUI Select 的大部分属性
- `size`: 尺寸
- `disabled`: 是否禁用
- `valueKey`: 作为 value 唯一标识的键名
- `collapseTags`: 多选时是否将选中值按文字的形式展示
- `multipleLimit`: 多选时用户最多可以选择的项目数

**Tree 相关属性：**
- 继承 ElementUI Tree 的大部分属性
- `showCheckbox`: 是否显示复选框
- `checkOnClickNode`: 是否在点击节点时选中节点
- `expandOnClickNode`: 是否在点击节点时展开节点
- `accordion`: 是否每次只打开一个同级树节点

**事件说明：**
- `@change`: 选中值变化事件
- `@remove-tag`: 移除标签事件（多选模式）
- `@clear`: 清空事件
- `@blur`: 失去焦点事件
- `@focus`: 获得焦点事件
- `@visible-change`: 下拉框显示/隐藏事件

**主要功能：**
- 结合 Select 和 Tree 的功能
- 支持单选和多选
- 支持搜索过滤
- 支持节点展开/收起
- 支持复选框模式

**适用场景：**
- 层级数据选择
- 下拉树形选择器
- 部门/组织选择
- 分类数据选择

### 2.8 CetIcon (图标组件)

**组件名称：** `CetIcon`

**使用规范：**
```vue
<CetIcon
  iconClass="test"
  class="I2 hover p2"
/>
```

**属性说明：**
- `iconClass`: String - 图标类名（必需），对应 SVG symbol 的 ID

**样式类说明：**
- 尺寸类：`I1`、`I2`、`I3`、`I4`、`I5` - 不同尺寸的图标
- 交互类：`hover` - 悬停效果，`active` - 激活状态
- 内边距类：`p0` 到 `p8` - 设置不同的内边距

**适用场景：**
- SVG 图标展示
- 按钮图标
- 状态指示图标
- 导航图标

### 2.9 CetTabs (标签页组件)

**组件名称：** `CetTabs`

**使用规范：**
```vue
<CetTabs v-model="activeTab">
  <SomeComponent1 label="标签1" name="tab1" />
  <SomeComponent2 label="标签2" name="tab2" />
  <SomeComponent3 label="标签3" name="tab3" />
</CetTabs>
```

**主要功能：**
- 基于 ElementUI Tabs 的高阶组件
- 内置 keep-alive 功能，组件切换时保持状态
- 支持权限控制（v-permission 指令）
- 自动处理组件生命周期（activated/deactivated）

**属性说明：**
- 继承 ElementUI Tabs 的所有属性
- 子组件需要设置 `label` 属性作为标签文本
- 可选设置 `name` 属性作为标签标识

**事件说明：**
- `@tab-click`: 标签点击事件

**适用场景：**
- 多页面内容切换
- 需要保持组件状态的标签页
- 复杂表单的分步骤展示

### 2.10 CetTransfer (穿梭框组件)

**组件名称：** `CetTransfer`

**使用规范：**
```vue
<CetTransfer
  :handlerData="getTreeData"
  :nodeKey="'tree_id'"
  :props="{ label: 'name', children: 'children' }"
  :checkedNodes="selectedNodes"
/>
```

**属性说明：**
- `handlerData`: Function - 获取树形数据的方法，返回 Promise
- `nodeKey`: String - 节点唯一标识字段，默认 'tree_id'
- `props`: Object - 树节点属性配置
  - `label`: 显示字段名，默认 'name'
  - `children`: 子节点字段名，默认 'children'
- `checkedNodes`: Array - 已选中的节点数组

**主要功能：**
- 左侧树形结构选择
- 右侧已选项目列表
- 支持搜索过滤
- 支持点击移除已选项

**适用场景：**
- 权限分配
- 组织架构选择
- 层级数据的批量选择

### 2.11 CetAside (侧边栏组件)

**组件名称：** `CetAside`

**使用规范：**
```vue
<CetAside @collapse="handleCollapse">
  <template #aside>
    <div>侧边栏内容</div>
  </template>
  <template #container>
    <div>主要内容区域</div>
  </template>
</CetAside>
```

**插槽说明：**
- `#aside`: 侧边栏内容插槽
- `#container`: 主内容区域插槽

**事件说明：**
- `@collapse`: 折叠状态变化事件，参数 `{ isCollapsed: boolean }`

**主要功能：**
- 可折叠的侧边栏布局
- 平滑的展开/收起动画
- 自适应的主内容区域

**适用场景：**
- 页面布局分区
- 导航侧边栏
- 工具面板布局

### 2.12 CetGiantTree (大数据量树形组件)

**组件名称：** `CetGiantTree`

**使用规范：**
```vue
<CetGiantTree
  :inputData_in="treeData"
  :setting="treeSetting"
  :checkedNodes.sync="selectedNodes"
  :selectNode.sync="currentNode"
  :showFilter="true"
  :openShiftCheck="true"
  @currentNode_out="handleNodeClick"
  @checkedNodes_out="handleNodesCheck"
/>
```

**属性说明：**
- `inputData_in`: Array - 树形数据
- `setting`: Object - zTree 配置对象
- `checkedNodes`: Array - 已勾选节点（支持 .sync）
- `selectNode`: Object - 当前选中节点（支持 .sync）
- `showFilter`: Boolean - 是否显示搜索框，默认 true
- `openShiftCheck`: Boolean - 是否支持 Shift 多选，默认 true
- `searchText_in`: String - 搜索关键字
- `unCheckTrigger_in`: Number - 取消勾选触发器
- `customSearch`: Function - 自定义搜索方法
- `isAutoCancelSelected`: Boolean - 是否允许 Ctrl 取消选择

**事件说明：**
- `@currentNode_out`: 节点点击事件
- `@checkedNodes_out`: 节点勾选事件
- `@searchText_out`: 搜索文本变化事件
- `@created_out`: 树初始化完成事件

**主要功能：**
- 支持大数据量树形结构
- 内置搜索过滤功能
- 支持 Shift 键多选
- 支持父子关联/不关联模式
- 支持层级节点快捷操作

**适用场景：**
- 大量层级数据展示
- 组织架构树
- 设备树形管理
- 权限树选择

### 2.13 CetSimpleSelect (简单选择器)

**组件名称：** `CetSimpleSelect`

**使用规范：**
```vue
<CetSimpleSelect
  v-model="selectedValue"
  :option="{ key: 'id', label: 'name', value: 'id', disabled: 'disabled' }"
  :interface="{
    queryMode: 'trigger',
    dataMode: 'interface',
    queryFunc: 'getSelectOptions',
    data: [],
    dynamicInput: {}
  }"
/>
```

**属性说明：**
- `option`: Object - 选项配置
  - `key`: 选项唯一标识字段
  - `label`: 显示文本字段
  - `value`: 选项值字段
  - `disabled`: 禁用状态字段
- `interface`: Object - 接口配置，用于动态获取选项数据
- 继承 ElementUI Select 的所有属性

**主要功能：**
- 结合 CetInterface 组件动态获取选项
- 支持接口数据联动
- 轻量级下拉选择

**适用场景：**
- 动态选项的下拉选择
- 接口数据联动选择
- 简单的数据选择场景

### 2.14 CetZtree (基于 zTree 的树形组件)

**组件名称：** `CetZtree`

**使用规范：**
```vue
<CetZtree
  :data="treeData"
  :setting="treeSetting"
  :hasSearch="true"
  :props="{ name: 'name', children: 'children' }"
  ref="ztreeRef"
/>
```

**属性说明：**
- `data`: Array - 树形数据
- `setting`: Object - zTree 配置对象
- `hasSearch`: Boolean - 是否显示搜索框，默认 true
- `props`: Object - 节点属性映射
  - `name`: 节点名称字段，默认 'name'
  - `children`: 子节点字段，默认 'children'

**方法说明：**
组件代理了 zTree 的常用方法，可通过 ref 调用：
- `getNodes()`: 获取所有节点
- `getSelectedNodes()`: 获取选中节点
- `getCheckedNodes()`: 获取勾选节点
- `selectNode(node)`: 选中指定节点
- `checkNode(node, checked)`: 勾选/取消勾选节点
- `expandAll(expand)`: 展开/收起所有节点

**主要功能：**
- 基于 zTree 的完整树形功能
- 内置模糊搜索
- 支持单选/多选模式
- 支持异步加载
- 支持节点编辑操作

**适用场景：**
- 复杂的树形数据操作
- 需要编辑功能的树组件
- 支持搜索的层级选择
- 异步加载的大型树结构

## 三、全局配置

### 3.1 组件库安装配置

在 main.js 中的配置示例：

```javascript
import CetCommon from "cet-common";
import { CustomElSelect, CustomElDatePicker } from "eem-base/components";

// 注册 eem-base 组件
Vue.component("CustomElDatePicker", CustomElDatePicker);
Vue.component("customElSelect", CustomElSelect);
Vue.component("CustomElSelect", CustomElSelect);

// 安装 cet-common
Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  }
});
```

### 3.2 默认配置说明

通过 `defaultSettings` 可以配置组件默认行为：

- `CetDialog.isDraggable`: 对话框是否可拖拽
- `CetTable.isColumsHeaderDoLayout`: 表格列头是否重新布局
- `CetGiantTree.isAutoCancelSelected`: 大树是否自动取消选择
- `CetDateSelect.isClosedEnd`: 日期选择是否闭合结束

## 四、最佳实践

### 4.1 命名规范
- 组件名使用 PascalCase
- 属性名使用 camelCase 或 snake_case（根据组件要求）
- 事件名使用 camelCase

### 4.2 使用建议
1. 优先使用组件库提供的组件，保持界面一致性
2. 合理使用触发器模式进行组件间通信
3. 充分利用组件的插槽功能进行自定义
4. 注意组件的生命周期和数据绑定

### 4.3 注意事项
- 触发器属性通常使用时间戳进行状态管理
- 组件数据绑定时注意深度监听和浅度监听的区别
- 使用 .sync 修饰符时要确保父组件正确处理数据变化
- 异步数据加载时要注意组件的生命周期
- 表格和树组件的大数据量处理要合理使用分页和懒加载

---

## 五、CET-CHART 图表组件库 (v1.7.5)

### 简介
`cet-chart` 是基于 ECharts 5.3.0 的 Vue 图表组件，提供了统一的图表解决方案，支持多主题切换、数据响应式更新、自动调整大小等功能。

### 5.1 安装和配置

**全局安装：**
```javascript
// main.js
import CetChart, { registerTheme } from "cet-chart";

Vue.use(CetChart, {
  themeConf: {
    // 自定义主题配置
    // backgroundColor: "#000"
  }
});
```

**组件导入：**
```javascript
// 单独导入
import CetChart from "cet-chart";

// 导入 ECharts 实例
import echarts from "cet-chart/echarts";
```

### 5.2 基本使用

**组件名称：** `CetChart`

**基础用法：**
```vue
<template>
  <div class="chart-container">
    <CetChart
      :options="chartOptions"
      :inputData_in="chartData"
      :autoresize="true"
      @click="handleChartClick"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      chartData: [
        { name: '一月', value: 120 },
        { name: '二月', value: 200 },
        { name: '三月', value: 150 }
      ],
      chartOptions: {
        title: {
          text: '月度数据统计'
        },
        tooltip: {
          trigger: 'item'
        },
        xAxis: {
          type: 'category'
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          encode: {
            x: 'name',
            y: 'value'
          }
        }]
      }
    };
  },
  methods: {
    handleChartClick(params) {
      console.log('图表点击事件:', params);
    }
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}
</style>
```

### 5.3 核心属性

**基础属性：**
- `options`: Object - ECharts 配置选项，必需
- `inputData_in`: Array/Object - 图表数据源，可选
- `initOptions`: Object - ECharts 初始化选项，可选
- `group`: String - 图表分组标识，用于图表联动，可选
- `autoresize`: Boolean - 是否自动调整大小，默认 true
- `watchShallow`: Boolean - 是否浅度监听 options 变化，默认 false
- `manualUpdate`: Boolean - 是否手动更新，默认 false

**属性详细说明：**

#### options (必需)
ECharts 的完整配置对象，包含图表的所有配置信息：
```javascript
options: {
  title: { text: '图表标题' },
  tooltip: { trigger: 'axis' },
  legend: { data: ['系列1', '系列2'] },
  xAxis: { type: 'category', data: ['A', 'B', 'C'] },
  yAxis: { type: 'value' },
  series: [
    {
      name: '系列1',
      type: 'line',
      data: [120, 200, 150]
    }
  ]
}
```

#### inputData_in (可选)
数据源，支持数组或对象格式。当提供此属性时，会自动设置 ECharts 的 dataset.source：
```javascript
// 数组格式
inputData_in: [
  { name: '产品A', value: 120, category: '类别1' },
  { name: '产品B', value: 200, category: '类别2' }
]

// 二维数组格式
inputData_in: [
  ['产品', '销量', '类别'],
  ['产品A', 120, '类别1'],
  ['产品B', 200, '类别2']
]
```

#### autoresize (默认: true)
是否自动调整图表大小。当容器尺寸变化时，图表会自动重新渲染：
```vue
<CetChart :options="options" :autoresize="true" />
```

#### group (可选)
图表分组，用于多图表联动：
```vue
<CetChart :options="options1" group="group1" />
<CetChart :options="options2" group="group1" />
```

### 5.4 事件处理

CetChart 支持所有 ECharts 事件，常用事件包括：

**鼠标事件：**
- `@click`: 点击事件
- `@dblclick`: 双击事件
- `@mouseover`: 鼠标悬停事件
- `@mouseout`: 鼠标离开事件
- `@mousemove`: 鼠标移动事件

**图表事件：**
- `@legendselectchanged`: 图例选择变化
- `@datazoom`: 数据缩放事件
- `@brush`: 刷选事件
- `@rendered`: 渲染完成事件
- `@finished`: 动画完成事件

**事件使用示例：**
```vue
<template>
  <CetChart
    :options="options"
    @click="handleClick"
    @legendselectchanged="handleLegendChange"
    @datazoom="handleDataZoom"
  />
</template>

<script>
export default {
  methods: {
    handleClick(params) {
      console.log('点击数据:', params);
      // params 包含点击的数据信息
    },
    handleLegendChange(params) {
      console.log('图例变化:', params);
    },
    handleDataZoom(params) {
      console.log('数据缩放:', params);
    }
  }
};
</script>
```

### 5.5 组件方法

CetChart 提供了丰富的方法来操作图表：

**数据操作方法：**
```javascript
// 通过 ref 调用组件方法
this.$refs.chart.mergeOptions(newOptions);  // 合并新配置
this.$refs.chart.appendData(params);        // 追加数据
this.$refs.chart.clear();                   // 清空图表
this.$refs.chart.dispose();                 // 销毁图表
```

**图表控制方法：**
```javascript
this.$refs.chart.resize();                  // 手动调整大小
this.$refs.chart.showLoading();             // 显示加载动画
this.$refs.chart.hideLoading();             // 隐藏加载动画
this.$refs.chart.getDataURL();              // 获取图表图片
```

**坐标转换方法：**
```javascript
this.$refs.chart.convertToPixel(finder, value);    // 逻辑坐标转像素坐标
this.$refs.chart.convertFromPixel(finder, value);  // 像素坐标转逻辑坐标
this.$refs.chart.containPixel(finder, value);      // 判断像素点是否在指定区域
```

### 5.6 主题系统

CetChart 内置了多个主题，支持动态切换：

**内置主题：**
- `dark`: 深色主题
- `light`: 浅色主题
- `blue`: 蓝色主题
- `bluex`: 深蓝主题

**主题自动切换：**
主题会根据 `localStorage` 中的 `omega_theme` 值自动切换。

**自定义主题：**
```javascript
import { registerTheme } from "cet-chart";

// 注册自定义主题
registerTheme('custom', {
  color: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
  backgroundColor: '#f8f9fa',
  textStyle: {
    color: '#333'
  }
});
```

### 5.7 常用图表类型配置

#### 5.7.1 折线图
```javascript
lineChartOptions: {
  title: {
    text: '趋势分析'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category'
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'line',
    smooth: true,
    encode: {
      x: 'date',
      y: 'value'
    }
  }]
}
```

#### 5.7.2 柱状图
```javascript
barChartOptions: {
  title: {
    text: '数据对比'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category'
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'bar',
    encode: {
      x: 'category',
      y: 'value'
    }
  }]
}
```

#### 5.7.3 饼图
```javascript
pieChartOptions: {
  title: {
    text: '占比分析'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  series: [{
    type: 'pie',
    radius: '50%',
    encode: {
      itemName: 'name',
      value: 'value'
    }
  }]
}
```

#### 5.7.4 散点图
```javascript
scatterChartOptions: {
  title: {
    text: '相关性分析'
  },
  tooltip: {
    trigger: 'item'
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'scatter',
    encode: {
      x: 'x',
      y: 'y'
    }
  }]
}
```

#### 5.7.5 仪表盘
```javascript
gaugeChartOptions: {
  series: [{
    type: 'gauge',
    detail: {
      formatter: '{value}%'
    },
    data: [{
      value: 70,
      name: '完成率'
    }]
  }]
}
```

### 5.8 数据绑定模式

#### 5.8.1 使用 inputData_in (推荐)
```vue
<template>
  <CetChart :options="options" :inputData_in="chartData" />
</template>

<script>
export default {
  data() {
    return {
      chartData: [
        { month: '1月', sales: 120, profit: 80 },
        { month: '2月', sales: 200, profit: 150 }
      ],
      options: {
        xAxis: { type: 'category' },
        yAxis: { type: 'value' },
        series: [{
          type: 'bar',
          encode: { x: 'month', y: 'sales' }
        }]
      }
    };
  }
};
</script>
```

#### 5.8.2 直接在 options 中配置数据
```vue
<template>
  <CetChart :options="options" />
</template>

<script>
export default {
  data() {
    return {
      options: {
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          data: [120, 200, 150]
        }]
      }
    };
  }
};
</script>
```

### 5.9 最佳实践

#### 5.9.1 性能优化
```javascript
// 1. 大数据量时使用数据采样
options: {
  series: [{
    type: 'line',
    sampling: 'average',  // 数据采样
    large: true,          // 大数据量优化
    data: largeDataArray
  }]
}

// 2. 合理使用 watchShallow
<CetChart :options="options" :watchShallow="true" />

// 3. 手动控制更新
<CetChart :options="options" :manualUpdate="true" ref="chart" />
// 手动更新
this.$refs.chart.mergeOptions(newOptions);
```

#### 5.9.2 响应式设计
```vue
<template>
  <div class="chart-wrapper">
    <CetChart :options="options" :autoresize="true" />
  </div>
</template>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 400px;
  min-height: 300px;
}

@media (max-width: 768px) {
  .chart-wrapper {
    height: 300px;
  }
}
</style>
```

### 5.10 注意事项

1. **容器尺寸**：确保图表容器有明确的宽高，否则图表可能无法正常显示
2. **数据格式**：使用 `inputData_in` 时，确保数据格式与 `encode` 配置匹配
3. **主题切换**：主题切换会触发图表重新初始化，注意性能影响
4. **内存管理**：组件销毁时会自动清理图表实例，无需手动处理
5. **事件处理**：避免在事件处理函数中进行大量计算，影响交互性能

### 5.11 常见问题

**Q: 图表不显示？**
A: 检查容器是否有明确的宽高，options 配置是否正确

**Q: 数据更新后图表不刷新？**
A: 确保数据是响应式的，或使用 `mergeOptions` 方法手动更新

**Q: 如何实现图表联动？**
A: 使用相同的 `group` 属性，或通过事件监听实现自定义联动

**Q: 如何导出图表？**
A: 使用 `getDataURL()` 方法获取图表的 base64 图片数据

