# fetch 工具使用规范

## 概述

`eem-base/utils/fetch.js` 是项目中的HTTP请求工具封装，基于 `@omega/http` 的 `HttpBase` 类实现，提供了统一的请求配置和两种不同使用场景的HTTP实例。

## 文件位置

```
node_modules/eem-base/utils/fetch.js
```

## 核心功能

### 1. 环境检测
- 自动检测开发环境，在开发模式下添加用户ID到请求头

### 2. 用户信息集成
- 自动获取当前登录用户信息并添加到请求头

### 3. 统一请求配置
- 默认响应类型：JSON
- POST请求Content-Type：`application/json;charset=UTF-8`
- 错误处理：不自动拒绝错误状态码

## 导出实例

### httping（默认导出）
标准HTTP请求实例，适用于常规的用户操作请求。

**特性：**
- 显示加载状态
- 包含完整的错误提示
- 适合用户主动触发的操作

### hideNoticeFetch（命名导出）
静默HTTP请求实例，适用于后台数据同步等场景。

**特性：**
- 不显示加载状态
- 静默执行，不干扰用户体验
- 适合轮询、自动保存等后台操作

## 使用方法

### 导入方式

```javascript
// 方式1：仅导入默认实例
import fetch from 'eem-base/utils/fetch';

// 方式2：仅导入静默实例
import { hideNoticeFetch } from 'eem-base/utils/fetch';

// 方式3：同时导入两个实例
import fetch, { hideNoticeFetch } from 'eem-base/utils/fetch';
```

### 基本用法

#### GET 请求

```javascript
import fetch from 'eem-base/utils/fetch';

// 获取数据
const response = await fetch.get('/api/users');

// 带参数的GET请求
const response = await fetch.get('/api/users', {
  params: {
    page: 1,
    size: 10
  }
});
```

#### POST 请求

```javascript
import fetch from 'eem-base/utils/fetch';

// 创建数据
const response = await fetch.post('/api/users', {
  name: '张三',
  email: '<EMAIL>'
});

// 带配置的POST请求
const response = await fetch.post('/api/upload', formData, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});
```

#### PUT/PATCH 请求

```javascript
import fetch from 'eem-base/utils/fetch';

// 更新数据
const response = await fetch.put('/api/users/123', {
  name: '李四',
  email: '<EMAIL>'
});

// 部分更新
const response = await fetch.patch('/api/users/123', {
  email: '<EMAIL>'
});
```

#### DELETE 请求

```javascript
import fetch from 'eem-base/utils/fetch';

// 删除数据
const response = await fetch.delete('/api/users/123');
```

### 静默请求用法

```javascript
import { hideNoticeFetch } from 'eem-base/utils/fetch';

// 轮询状态检查（不显示加载状态）
const checkStatus = async () => {
  const response = await hideNoticeFetch.get('/api/status');
  return response.data;
};

// 自动保存草稿
const autoSave = async (data) => {
  await hideNoticeFetch.post('/api/draft/save', data);
};

// 后台数据同步
const syncData = async () => {
  await hideNoticeFetch.put('/api/sync', syncPayload);
};
```

## 使用场景

### 使用 httping（默认导出）的场景：
- 用户点击按钮触发的操作
- 表单提交
- 数据查询和展示
- 需要用户感知的操作

### 使用 hideNoticeFetch 的场景：
- 定时轮询
- 自动保存功能
- 后台数据同步
- 不需要用户感知的操作

## 错误处理

```javascript
import fetch from 'eem-base/utils/fetch';

try {
  const response = await fetch.get('/api/data');
  
  // 检查响应状态
  if (response.success) {
    console.log('请求成功:', response.data);
  } else {
    console.error('业务错误:', response.message);
  }
} catch (error) {
  console.error('网络错误:', error);
}
```

## 注意事项

1. **环境差异**：开发环境会自动添加User-ID请求头，生产环境不会
2. **错误处理**：两个实例都设置了 `rejectErrorCode: false`，需要手动检查响应状态
3. **加载状态**：只有默认导出的实例会显示加载状态
4. **请求头**：POST请求会自动设置JSON格式的Content-Type

## 最佳实践

1. **选择合适的实例**：根据用户体验需求选择是否显示加载状态
2. **统一错误处理**：建议封装统一的错误处理逻辑
3. **请求拦截**：可以基于这两个实例进一步封装业务相关的请求方法
4. **类型安全**：在TypeScript项目中建议添加响应类型定义

## 相关依赖

- `@omega/http`：HTTP请求基础库
- `@altair/knight`：用户信息获取API

## 更新日志

- 初始版本：提供基础的HTTP请求封装功能
