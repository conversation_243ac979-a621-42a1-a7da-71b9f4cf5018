---
type: "manual"
---

# 项目全局规范.mdc

---

## 1. 国际化规则

# AI 代码生成国际化规则（最新版）

## 规则说明

1. 所有界面/业务写死中文（如提示语、按钮文本等）必须用 $T('中文') 包裹，参数为原始字符串。
2. 所有中英文对照字段必须写入 src/config/lang/en.json，结构为 { "中文": "英文" }，如无则自动新增。
3. 英文 value 由 AI 自动翻译生成。
4. 代码生成前: console.log("谐波电压频谱");
   代码生成后: console.log($T("谐波电压频谱"));
   en.json 自动新增：{ "谐波电压频谱": "Harmonic Voltage Spectrum" }
5. 如遇特殊名词或缩写，优先保留原文或人工校对。
6. 本规则适用于所有新生成代码，AI 需严格遵循。
7. **必须使用大写的 $T，禁止使用小写的 $t。**
8. **出现中文名称时，必须在 src/config/lang/en.json 增加对应的中英文对照字段，若已存在该中文字段则无需重复添加。**
9. **在 src/config/lang/en.json 文件中增加的字段必须是平铺的，不能出现嵌套结构。**

## 目录结构

- 业务代码：所有写死中文用 $T 包裹
- 国际化资源：src/config/lang/en.json（所有中英文对照字段必须写入此文件）

## 示例

```vue
<!-- 生成前 -->
<button>+ 添加用户</button>
<h2>谐波电压频谱</h2>

<!-- 生成后 -->
<button>{{ $T('+ 添加用户') }}</button>
<h2>{{ $T('谐波电压频谱') }}</h2>
```

en.json:

```json
{
  "+ 添加用户": "+ Add User",
  "谐波电压频谱": "Harmonic Voltage Spectrum"
}
```

### 进阶用法

```vue
<!-- 属性绑定、指令等场景 -->
<el-button :title="$T('点击添加用户')">{{ $T('+ 添加用户') }}</el-button>
<span v-if="show">{{ $T('显示内容') }}</span>
```

---

## 2. 生成简单目录规则

# 生成一级目录和路由.mdc（英文目录名规范优化版）

## 1. 目录结构

- 每新增一级业务模块，必须在 `src/projects/模块英文名/` 下新建对应目录，目录名必须为英文（如 exercise、report、userCenter 等）。
- 每个一级模块目录下，必须包含：
  - `router.js`：本模块的一级页面路由注册。
  - `index.vue`：一级页面主组件。

## 2. 路由注册

- `src/projects/模块英文名/router.js` 只声明本模块的一级页面路由，导出为数组。
- 路由 path、name 必须与目录英文名一致。
- `src/router/index.js` 负责聚合所有一级模块的路由。
- 示例：

  ```js
  // src/projects/exercise/router.js
  export default [
    {
      path: "/exercise", // 路由 path 与目录英文名一致
      name: "exercise", // 路由 name 与目录英文名一致
      meta: { title: "练习" },
      component: () => import("./index.vue")
    }
  ];
  ```

  ```js
  // src/router/index.js
  import exerciseRoutes from "@/projects/exercise/router.js";
  export default [
    ...exerciseRoutes
    // ...其他一级模块
  ];
  ```

## 3. 导航菜单同步

- 每新增一级模块，必须在 `src/config/config.js` 的 navmenu 中新增一级菜单项。
- location、permission 字段必须与目录英文名一致（如 /exercise、/report）。
- 示例：
  ```js
  {
    label: $T("练习"),
    type: "menuItem",
    icon: "el-icon-edit", // 可自定义
    color: "",
    location: "/exercise",    // 与目录英文名一致
    permission: "/exercise"    // 与目录英文名一致
  }
  ```

## 4. 国际化

- 一级菜单名、页面标题等，必须在国际化文件中补全中英文。
- 英文目录名仅用于技术实现，界面显示仍用中英文。

## 5. 变更流程

- 新增/修改一级页面时，**必须同步维护 router.js、src/router/index.js、config.js、国际化文件**，保证导航、路由、菜单一致。

---

## 代码示例

### 1. src/projects/exercise/router.js

```js
export default [
  {
    path: "/exercise",
    name: "exercise",
    meta: { title: "练习" },
    component: () => import("./index.vue")
  }
];
```

### 2. src/router/index.js

```js
import exerciseRoutes from "@/projects/exercise/router.js";
export default [
  ...exerciseRoutes
  // ...其他一级模块
];
```

### 3. src/config/config.js

```js
{
  label: $T("练习"),
  type: "menuItem",
  icon: "el-icon-edit",
  color: "",
  location: "/exercise",
  permission: "/exercise"
}
```

---

## 6. 菜单与路由一致性

- config.js 的 navmenu 中，所有 location 和 permission 字段必须与 src/projects/模块英文名/router.js 中的 path 完全一致，均为英文目录名。

---

## 7. 代码风格要求

- 所有生成的代码必须是 vue2 格式（export default、data、mounted、methods、组件注册等均用 Vue2 语法）。

---

如需自动补全和修正相关文件，请直接告知模块英文名和中英文标题，我会自动补全相关文件内容。

---

## 3. 样式规则

# AI 样式生成规则（AI Style Generation Rules）

本规则用于指导 AI 及团队成员在生成样式时，如何结合 Tailwind CSS 工具类与 tailwind.var.css 中的 CSS 变量，确保主题一致性、可维护性和高复用性。

---

## 0. 变量与 Tailwind 工具类对应关系

| 变量名 | 说明         | 推荐 Tailwind 工具类     | 示例用法（变量写法） |
| ------ | ------------ | ------------------------ | -------------------- |
| --BG   | 主背景色     | bg-gray-100              | bg-BG                |
| --BG1  | 内容背景色   | bg-white                 | bg-BG1               |
| --BG2  | 滑入背景色   | bg-gray-200              | bg-BG2               |
| --BG3  | 点击背景色   | bg-gray-300              | bg-BG3               |
| --BG4  | 选中背景色   | bg-green-50              | bg-BG4               |
| --ZS   | 主色         | bg-green-500             | bg-ZS                |
| --F1   | 辅助色 1     | bg-green-700             | bg-F1                |
| --F2   | 辅助色 2     | bg-red-500               | bg-F2                |
| --T1   | 主要文字色   | text-gray-900            | text-T1              |
| --T2   | 常规文字色   | text-gray-700            | text-T2              |
| --T3   | 次要文字色   | text-gray-500            | text-T3              |
| --T4   | 占位文字色   | text-gray-400            | text-T4              |
| --T5   | 反色/白字    | text-white               | text-T5              |
| --B1   | 主边框色     | border-gray-300          | border-B1            |
| --B2   | 次边框色     | border-gray-100          | border-B2            |
| --J1   | 间距 1(8px)  | p-2/m-2                  | p-J1/m-J1            |
| --J2   | 间距 2(16px) | p-4/m-4                  | p-J2/m-J2            |
| --J3   | 间距 3(24px) | p-6/m-6                  | p-J3/m-J3            |
| --J4   | 间距 4(32px) | p-8/m-8                  | p-J4/m-J4            |
| --I1   | 图标尺寸 1   | w-6 h-6                  | w-I1 h-I1            |
| --I2   | 图标尺寸 2   | w-8 h-8                  | w-I2 h-I2            |
| --I3   | 图标尺寸 3   | w-10 h-10                | w-I3 h-I3            |
| --I4   | 图标尺寸 4   | w-16 h-16                | w-I4 h-I4            |
| --I5   | 图标尺寸 5   | w-24 h-24                | w-I5 h-I5            |
| --H    | 标题字号     | text-2xl                 | text-H               |
| --H1   | 副标题字号   | text-xl                  | text-H1              |
| --H2   | 小标题字号   | text-lg                  | text-H2              |
| --H3   | 正文字号     | text-base                | text-H3              |
| --Aa   | 辅助字号 1   | text-sm                  | text-Aa              |
| --Ab   | 辅助字号 2   | text-xs                  | text-Ab              |
| --S1   | 强投影       | shadow-md                | shadow-S1            |
| --S2   | 浅色投影     | shadow-lg                | shadow-S2            |
| --SCR  | 滚动条色     | scrollbar-thumb-gray-400 | scrollbar-thumb-SCR  |

> 以上为常用变量与 Tailwind 工具类的推荐对应关系，具体可根据设计需求调整。

---

## 1. 变量优先原则

- 涉及颜色、间距、字体、阴影等样式时，优先使用 tailwind.var.css 中的变量（如 `var(--BG1)`、`var(--T1)` 等），避免硬编码数值。
- 变量未覆盖的属性，可用 Tailwind 原生工具类或自定义数值。

---

## 2. 模板层写法建议

- 模板层可直接写 Tailwind 工具类。
- 若工具类涉及变量，建议用 `xxx-xxx` 形式。
- 变量和 Tailwind 工具类可混用，变量优先。

---

## 3. 高频组合样式封装

- 发现多处出现的相同样式组合，应封装为独立类名（如 `.card`、`.btn-primary`），并在 CSS 中用变量实现。
- 模板层直接用类名，提升复用性和维护性。

---

## 4. 主题切换兼容

- 所有用变量实现的样式，均自动适配 `[data-theme]` 主题切换，无需手动切换类名。

---

## 5. 变量优先级建议

- 主题色、文字色、背景色、边框色、间距、尺寸、阴影等，优先用变量。
- 变量未覆盖时再用 Tailwind 工具类。

---

## 6. 代码片段参考

### 按钮

```css
.btn-primary {
  background-color: var(--ZS);
  color: var(--T5);
  padding: var(--J1) var(--J2);
  border-radius: 4px;
  box-shadow: var(--S1);
}
```

```html
<button class="btn-primary">$T("按钮")</button>
```

### 输入框

```html
<input class="bg-BG1 text-T1 border-B1 p-J1 rounded-[4px] focus:border-ZS" />
```

---

## 4. cet-chart 组件强制规范

# cet-chart 组件强制使用与模板规范（AI 自动生成专用）【优化版】

## 0. 优先使用 cet-chart 的强制要求

- 凡出现 ECharts 图形类需求，**第一时间必须优先使用 cet-chart**，除非明确指定使用 cet-graph 或 omega-trend。
- **当出现“echarts 图形”或类似字眼时，必须使用的是 cet-chart，而不是 echarts。**

## 1. 组件强制要求

- 本项目所有 ECharts 相关功能，**必须**通过 cet-chart 组件（如 `<CetChart>` 或 `CetChartFactory`）实现。
- **禁止**直接引用、操作 echarts 实例、API 或其原生组件（如 `import echarts`、`echarts.init`、`new echarts` 等）。
- 包括但不限于图表渲染、主题切换、地图注册等，均应通过 cet-chart 提供的接口完成。

## 2. 模板层用法与变量命名规范

- 模板层必须使用如下写法：
  ```vue
  <CetChart v-bind="CetChart_英文名称" />
  ```
- **禁止在脚本中 import CetChart from 'cet-chart'，也禁止在 components 注册，直接在模板层使用 <CetChart ... />。**
- 其中 `CetChart_英文名称` 为 data/computed/methods/setup 中定义的对象，命名规则为 `CetChart_` + 图形功能英文名（如 VoltageWaveRecording、FaultTypeBar、DeviceProblemPie 等）。
- 如有多个图表，分别命名为 CetChart*英文名称 1、CetChart*英文名称 2 等。

## 3. options 配置要求（重点优化）

- `CetChart_英文名称` 对象结构如下：
  ```js
  CetChart_英文名称 = {
    inputData_in: null, // 如有数据源则传，否则为 null
    options: {
      /* ECharts 配置对象，按原型图片或需求定制 */
    }
  };
  ```
- options 属性必须为标准 ECharts 配置，类型、坐标轴、series、grid 等均需根据原型图片或需求定制。
- **无论是否有数据，options 的结构和所有参数都必须完整配置。**
  - 即使数据为空（如 data: []），也必须初始化 options，确保包含所有必要的结构（如 title、legend、xAxis、yAxis、series 等），避免初始渲染异常或图形区域为空白。
  - 示例（无数据时）：
    ```js
    CetChart_Example = {
      inputData_in: null,
      options: {
        title: { text: $T("示例图表") },
        legend: { data: [] },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value" },
        series: [{ name: $T("示例"), type: "bar", data: [] }]
      }
    };
    ```
- 禁止仅在有数据时才生成 options，必须在组件初始化时就配置好完整结构。

## 4. 常见错误与反例

### 4.1 错误示例

- **错误写法 1：仅在有数据时才配置 options**
  ```js
  if (data.length) {
    CetChart_Example = {
      options: {
        /* ...完整配置... */
      }
    };
  }
  // 没有数据时未配置 options，导致图表渲染异常
  ```
- **错误写法 2：options 结构不完整**
  ```js
  CetChart_Example = {
    options: {
      series: []
      // 缺少 title、legend、xAxis、yAxis 等
    }
  };
  ```

### 4.2 正确示例

- **无论有无数据，options 结构都完整**
  ```js
  CetChart_Example = {
    inputData_in: null,
    options: {
      title: { text: $T("示例图表") },
      legend: { data: [] },
      xAxis: { type: "category", data: [] },
      yAxis: { type: "value" },
      series: [{ name: $T("示例"), type: "bar", data: [] }]
    }
  };
  ```

## 5. 代码检查建议

- 推荐在代码评审或自动化检测中，检查 `CetChart_` 相关对象的 `options` 字段，确保其结构完整且初始化时已配置所有必要参数。
- 检查模板 `<CetChart v-bind="CetChart_英文名称" />` 是否对应 data/computed/methods/setup 中的对象，且对象结构符合规范。

## 6. 业务开发建议

- **初始化时即配置好 options**，后续仅通过响应式数据（如 series.data）动态更新数据部分，避免因 options 结构变化导致的渲染异常。
- **多图表场景**：每个图表都应有独立的 `CetChart_英文名称` 对象，且 options 结构独立、完整。
- **主题切换、空数据、加载中等场景**，options 结构都必须完整，series.data 允许为空数组，但不能缺失 series 配置。

## 7. FAQ

- **Q: 没有数据时，图表会不会显示异常？**  
  A: 只要 options 结构完整，series.data 为空数组时，ECharts 会正常渲染空图表区域，不会报错或异常。
- **Q: 可以只传 series 吗？**  
  A: 不可以。必须包含 title、legend、xAxis、yAxis、series 等完整结构。
- **Q: 可以直接 import echarts 吗？**  
  A: 严禁。所有 ECharts 相关操作必须通过 cet-chart 组件完成

=

---

## 5. omega-http 强制使用规则

# omega-http 强制使用规则（AI 生成请求专用）

## 1. HTTP 请求唯一入口

项目中所有 HTTP/HTTPS 请求（包括 GET、POST、PUT、DELETE、PATCH 等）必须通过 omega-http 提供的 http、httping 实例或 OmegaHttpPlugin 进行发起。严禁直接使用 axios、fetch、XMLHttpRequest 或其他第三方 HTTP 客户端。

## 2. 禁止绕过拦截器与全局处理

不允许通过自定义 axios 实例、fetch 等方式绕过 omega-http 的全局拦截器、token 注入、国际化 header、loading、错误处理等机制。

## 3. 禁止全局 headers 设置 Authorization

禁止在全局 headers 或 setHeadersOperate 直接设置 Authorization 字段。如需携带 token，必须通过 omega-http 的 auth 机制或拦截器实现。

## 4. 引入方式规范

所有 http 的引入必须用 import { http } from "@omega/http"; 这样的方式，禁止其他写法。

---

description:
globs:
alwaysApply: false

---
