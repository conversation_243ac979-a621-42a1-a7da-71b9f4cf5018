# CustomElDatePicker - 自定义日期选择器

## 组件概述

`CustomElDatePicker` 是基于 ElementUI DatePicker 组件的增强版本，提供了带前缀标签的日期选择器功能。

## 组件名称

- `CustomElDatePicker`

## 基本用法

```vue
<template>
  <div>
    <CustomElDatePicker 
      prefix_in="选择日期" 
      v-model="dateValue"
      type="date"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      dateValue: ''
    };
  }
};
</script>
```

## 属性说明

### 自定义属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| prefix_in | String | - | 前缀标签文本（必需） |

### 继承属性

继承 ElementUI DatePicker 的所有属性，包括但不限于：

- `value / v-model`: 绑定值
- `type`: 显示类型（date/datetime/week/month/year/daterange等）
- `format`: 显示在输入框中的格式
- `value-format`: 绑定值的格式
- `placeholder`: 占位符
- `disabled`: 是否禁用
- `clearable`: 是否显示清除按钮
- `size`: 输入框尺寸
- `picker-options`: 当前时间日期选择器特有的选项

## 事件说明

继承 ElementUI DatePicker 的所有事件：

- `change`: 用户确认选定的值时触发
- `blur`: 当 input 失去焦点时触发
- `focus`: 当 input 获得焦点时触发

## 使用示例

### 基础日期选择

```vue
<CustomElDatePicker 
  prefix_in="开始日期" 
  v-model="startDate"
  type="date"
  placeholder="请选择开始日期"
/>
```

### 日期时间选择

```vue
<CustomElDatePicker 
  prefix_in="创建时间" 
  v-model="createTime"
  type="datetime"
  format="yyyy-MM-dd HH:mm:ss"
  value-format="yyyy-MM-dd HH:mm:ss"
/>
```

### 日期范围选择

```vue
<CustomElDatePicker 
  prefix_in="时间范围" 
  v-model="dateRange"
  type="daterange"
  range-separator="至"
  start-placeholder="开始日期"
  end-placeholder="结束日期"
/>
```

### 月份选择

```vue
<CustomElDatePicker 
  prefix_in="统计月份" 
  v-model="month"
  type="month"
  placeholder="请选择月份"
/>
```

### 年份选择

```vue
<CustomElDatePicker 
  prefix_in="统计年份" 
  v-model="year"
  type="year"
  placeholder="请选择年份"
/>
```

### 带限制的日期选择

```vue
<CustomElDatePicker 
  prefix_in="截止日期" 
  v-model="deadline"
  type="date"
  :picker-options="pickerOptions"
/>

<script>
export default {
  data() {
    return {
      deadline: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期
        }
      }
    };
  }
};
</script>
```

### 自定义格式

```vue
<CustomElDatePicker 
  prefix_in="生日" 
  v-model="birthday"
  type="date"
  format="yyyy年MM月dd日"
  value-format="yyyy-MM-dd"
/>
```

## 样式特性

- 带边框和圆角的自定义样式
- 前缀标签与日期选择器的统一视觉效果
- 与项目整体UI风格保持一致
- 支持不同尺寸的适配

## 适用场景

1. **需要带前缀标签的日期选择器**
   - 表单中需要明确标识日期字段用途的场景
   - 需要统一标签样式的日期选择器

2. **自定义样式的日期选择器**
   - 需要与项目UI风格保持一致的日期选择器
   - 需要特殊视觉效果的日期选择器

3. **各种日期选择场景**
   - 单日期选择
   - 日期时间选择
   - 日期范围选择
   - 月份/年份选择

## 注意事项

1. `prefix_in` 属性是必需的，用于显示前缀标签
2. 组件完全继承 ElementUI DatePicker 的功能，可以使用所有原生属性和事件
3. 日期格式化时注意 `format`（显示格式）和 `value-format`（绑定值格式）的区别
4. 使用日期范围选择时，绑定值是一个包含开始和结束日期的数组
5. 在表单验证中，验证规则应该绑定到 v-model 的值上
6. 样式已经预设，通常不需要额外的CSS调整

## 常用配置

### 日期限制配置

```javascript
// 只能选择今天及以后的日期
pickerOptions: {
  disabledDate(time) {
    return time.getTime() < Date.now() - 8.64e7;
  }
}

// 只能选择最近30天
pickerOptions: {
  disabledDate(time) {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return time.getTime() < thirtyDaysAgo.getTime() || time.getTime() > now.getTime();
  }
}
```

### 快捷选项配置

```javascript
// 日期范围快捷选项
pickerOptions: {
  shortcuts: [{
    text: '最近一周',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', [start, end]);
    }
  }, {
    text: '最近一个月',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      picker.$emit('pick', [start, end]);
    }
  }]
}
```

## 相关组件

- [CustomElSelect](./CustomElSelect.md) - 自定义选择器
- [CetDateSelect](./CetDateSelect.md) - 日期选择组件
