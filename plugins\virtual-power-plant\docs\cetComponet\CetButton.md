# CetButton - 按钮组件

## 组件概述

`CetButton` 是基于 ElementUI Button 组件的增强版本，提供了显示/隐藏控制、禁用状态控制和触发器模式等功能。

## 组件名称

- `CetButton`

## 基本用法

```vue
<template>
  <div>
    <CetButton
      :visible_in="true"
      :disable_in="false"
      title="确定"
      size="small"
      type="primary"
      icon="el-icon-check"
      @statusTrigger_out="handleClick"
    />
  </div>
</template>

<script>
export default {
  methods: {
    handleClick(timestamp) {
      console.log('按钮点击时间戳:', timestamp);
      // 处理按钮点击逻辑
    }
  }
};
</script>
```

## 属性说明

### 自定义属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible_in | Boolean | true | 是否显示按钮 |
| disable_in | Boolean | false | 是否禁用按钮 |
| title | String | - | 按钮显示文本 |
| size | String | 'small' | 按钮尺寸 |

### 继承属性

继承 ElementUI Button 的所有属性：

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| type | String | - | 按钮类型（primary/success/warning/danger/info/text） |
| plain | Boolean | false | 是否朴素按钮 |
| round | Boolean | false | 是否圆角按钮 |
| circle | Boolean | false | 是否圆形按钮 |
| loading | Boolean | false | 是否加载中状态 |
| icon | String | - | 图标类名 |
| autofocus | Boolean | false | 是否默认聚焦 |

### 尺寸选项

- `medium`: 中等尺寸
- `small`: 小尺寸（默认）
- `mini`: 迷你尺寸

## 事件说明

### 自定义事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| statusTrigger_out | timestamp | 点击时触发，返回当前时间戳 |

### 继承事件

继承 ElementUI Button 的所有事件：

- `click`: 点击事件
- `focus`: 获得焦点事件
- `blur`: 失去焦点事件

## 使用示例

### 基础按钮

```vue
<template>
  <div>
    <CetButton title="默认按钮" @statusTrigger_out="handleDefault" />
    <CetButton title="主要按钮" type="primary" @statusTrigger_out="handlePrimary" />
    <CetButton title="成功按钮" type="success" @statusTrigger_out="handleSuccess" />
    <CetButton title="警告按钮" type="warning" @statusTrigger_out="handleWarning" />
    <CetButton title="危险按钮" type="danger" @statusTrigger_out="handleDanger" />
  </div>
</template>

<script>
export default {
  methods: {
    handleDefault(timestamp) {
      console.log('默认按钮点击:', timestamp);
    },
    handlePrimary(timestamp) {
      console.log('主要按钮点击:', timestamp);
    },
    handleSuccess(timestamp) {
      console.log('成功按钮点击:', timestamp);
    },
    handleWarning(timestamp) {
      console.log('警告按钮点击:', timestamp);
    },
    handleDanger(timestamp) {
      console.log('危险按钮点击:', timestamp);
    }
  }
};
</script>
```

### 带图标的按钮

```vue
<template>
  <div>
    <CetButton 
      title="搜索" 
      type="primary" 
      icon="el-icon-search"
      @statusTrigger_out="handleSearch" 
    />
    <CetButton 
      title="编辑" 
      type="success" 
      icon="el-icon-edit"
      @statusTrigger_out="handleEdit" 
    />
    <CetButton 
      title="删除" 
      type="danger" 
      icon="el-icon-delete"
      @statusTrigger_out="handleDelete" 
    />
  </div>
</template>

<script>
export default {
  methods: {
    handleSearch(timestamp) {
      console.log('搜索按钮点击:', timestamp);
    },
    handleEdit(timestamp) {
      console.log('编辑按钮点击:', timestamp);
    },
    handleDelete(timestamp) {
      console.log('删除按钮点击:', timestamp);
    }
  }
};
</script>
```

### 状态控制按钮

```vue
<template>
  <div>
    <CetButton 
      title="保存" 
      type="primary"
      :disable_in="isSaving"
      :loading="isSaving"
      @statusTrigger_out="handleSave" 
    />
    
    <CetButton 
      title="条件按钮" 
      type="success"
      :visible_in="showConditionalButton"
      @statusTrigger_out="handleConditional" 
    />
    
    <el-checkbox v-model="showConditionalButton">显示条件按钮</el-checkbox>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isSaving: false,
      showConditionalButton: true
    };
  },
  methods: {
    async handleSave(timestamp) {
      console.log('保存按钮点击:', timestamp);
      this.isSaving = true;
      
      try {
        // 模拟保存操作
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.$message.success('保存成功');
      } catch (error) {
        this.$message.error('保存失败');
      } finally {
        this.isSaving = false;
      }
    },
    handleConditional(timestamp) {
      console.log('条件按钮点击:', timestamp);
    }
  }
};
</script>
```

### 不同尺寸的按钮

```vue
<template>
  <div>
    <CetButton title="中等按钮" size="medium" type="primary" />
    <CetButton title="小型按钮" size="small" type="primary" />
    <CetButton title="迷你按钮" size="mini" type="primary" />
  </div>
</template>
```

### 表单操作按钮组

```vue
<template>
  <div class="form-actions">
    <CetButton 
      title="取消" 
      @statusTrigger_out="handleCancel" 
    />
    <CetButton 
      title="重置" 
      type="warning"
      @statusTrigger_out="handleReset" 
    />
    <CetButton 
      title="提交" 
      type="primary"
      :disable_in="!formValid"
      @statusTrigger_out="handleSubmit" 
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      formValid: false
    };
  },
  methods: {
    handleCancel(timestamp) {
      console.log('取消操作:', timestamp);
      this.$emit('cancel');
    },
    handleReset(timestamp) {
      console.log('重置操作:', timestamp);
      this.$emit('reset');
    },
    handleSubmit(timestamp) {
      console.log('提交操作:', timestamp);
      if (this.formValid) {
        this.$emit('submit');
      }
    }
  }
};
</script>

<style scoped>
.form-actions {
  text-align: right;
  margin-top: 20px;
}

.form-actions .cet-button {
  margin-left: 10px;
}
</style>
```

### 触发器模式按钮

```vue
<template>
  <div>
    <CetButton 
      title="查询数据" 
      type="primary"
      @statusTrigger_out="triggerQuery" 
    />
    
    <CetButton 
      title="刷新列表" 
      type="success"
      @statusTrigger_out="triggerRefresh" 
    />
    
    <!-- 使用触发器控制其他组件 -->
    <CetTable 
      :queryTrigger_in="queryTrigger"
      :refreshTrigger_in="refreshTrigger"
      :data="tableData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryTrigger: 0,
      refreshTrigger: 0,
      tableData: []
    };
  },
  methods: {
    triggerQuery(timestamp) {
      console.log('触发查询:', timestamp);
      this.queryTrigger = timestamp;
    },
    triggerRefresh(timestamp) {
      console.log('触发刷新:', timestamp);
      this.refreshTrigger = timestamp;
    }
  }
};
</script>
```

## 主要功能

1. **基于 ElementUI Button 的增强组件**
   - 继承所有原生功能
   - 保持API兼容性

2. **显示/隐藏控制**
   - 通过 `visible_in` 属性控制按钮显示
   - 支持动态显示控制

3. **禁用状态控制**
   - 通过 `disable_in` 属性控制按钮禁用
   - 支持动态禁用控制

4. **触发器模式**
   - 点击时自动生成时间戳
   - 便于组件间通信

## 适用场景

1. **统一样式的操作按钮**
   - 表单提交按钮
   - 操作确认按钮
   - 功能触发按钮

2. **需要状态控制的按钮**
   - 条件显示的按钮
   - 动态禁用的按钮
   - 加载状态按钮

3. **触发器模式的按钮**
   - 组件间通信
   - 事件触发
   - 状态同步

## 注意事项

1. `title` 属性用于设置按钮文本，等同于插槽内容
2. `statusTrigger_out` 事件返回时间戳，便于触发器模式使用
3. 组件完全继承 ElementUI Button 的功能和样式
4. `visible_in` 和 `disable_in` 支持响应式数据绑定
5. 默认尺寸为 `small`，可根据需要调整
6. 可以同时使用自定义事件和原生事件

## 相关组件

- [CetDialog](./CetDialog.md) - 对话框组件
- [CetForm](./CetForm.md) - 表单组件
- [CetTable](./CetTable.md) - 表格组件
