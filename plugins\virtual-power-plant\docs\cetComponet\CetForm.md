# CetForm - 表单组件

## 组件概述

`CetForm` 是基于 ElementUI Form 组件的增强版本，支持多种数据获取模式、触发器模式和数据绑定功能，适用于复杂表单处理场景。

## 组件名称

- `CetForm`

## 基本用法

```vue
<template>
  <div>
    <CetForm
      :data="formData"
      dataMode="static"
      queryMode="trigger"
      :dataConfig="dataConfig"
      :rules="validationRules"
      :refreshAfterActived="false"
      @currentData_out="handleDataChange"
      @finishTrigger_out="handleFinish"
    >
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="formData.email"></el-input>
      </el-form-item>
      <el-form-item label="角色" prop="role">
        <el-select v-model="formData.role" placeholder="请选择角色">
          <el-option label="管理员" value="admin"></el-option>
          <el-option label="用户" value="user"></el-option>
        </el-select>
      </el-form-item>
    </CetForm>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        email: '',
        role: ''
      },
      dataConfig: {},
      validationRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    handleDataChange(data) {
      console.log('表单数据变化:', data);
    },
    handleFinish(timestamp) {
      console.log('操作完成:', timestamp);
    }
  }
};
</script>
```

## 属性说明

### 核心属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Object | {} | 表单数据对象 |
| dataMode | String | 'static' | 数据获取模式 |
| queryMode | String | 'trigger' | 查询模式 |
| dataConfig | Object | {} | 数据绑定配置 |
| rules | Object | {} | 表单验证规则 |
| refreshAfterActived | Boolean | false | 激活后是否刷新 |

### 数据模式说明

#### dataMode 选项
- `backendInterface`: 后端接口模式
- `component`: 组件数据模式
- `static`: 静态数据模式（默认）

#### queryMode 选项
- `trigger`: 触发器模式（按钮触发）
- `diff`: 差异模式（数据变化立即触发）

### 输入属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| inputData_in | Object | - | 外部输入数据 |
| queryId_in | Number | -1 | 查询数据的ID |

### 触发器属性

| 属性名 | 类型 | 说明 |
|--------|------|------|
| queryTrigger_in | Number | 查询触发器 |
| saveTrigger_in | Number | 保存触发器 |
| localSaveTrigger_in | Number | 本地保存触发器 |
| resetTrigger_in | Number | 重置触发器 |

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| currentData_out | data | 当前数据变化事件 |
| finishTrigger_out | timestamp | 操作完成事件 |
| saveData_out | data | 保存数据事件 |

## 方法说明

| 方法名 | 参数 | 说明 |
|--------|------|------|
| validate | callback | 表单验证 |
| validateField | props, callback | 验证指定字段 |
| resetFields | - | 重置表单字段 |
| clearValidate | props | 清除验证结果 |

## 使用示例

### 基础表单

```vue
<template>
  <div>
    <CetForm 
      :data="userForm" 
      :rules="userRules"
      ref="userForm"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="userForm.username" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input type="password" v-model="userForm.password" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input type="password" v-model="userForm.confirmPassword" placeholder="请确认密码"></el-input>
      </el-form-item>
    </CetForm>
    
    <div class="form-actions">
      <CetButton title="重置" @statusTrigger_out="resetForm" />
      <CetButton title="提交" type="primary" @statusTrigger_out="submitForm" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userForm: {
        username: '',
        password: '',
        confirmPassword: ''
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.userForm.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    },
    resetForm() {
      this.$refs.userForm.resetFields();
    },
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          console.log('表单验证通过:', this.userForm);
          // 提交表单逻辑
        } else {
          console.log('表单验证失败');
        }
      });
    }
  }
};
</script>
```

### 触发器模式表单

```vue
<template>
  <div>
    <div class="form-toolbar">
      <CetButton title="查询数据" @statusTrigger_out="triggerQuery" />
      <CetButton title="保存" type="primary" @statusTrigger_out="triggerSave" />
      <CetButton title="重置" @statusTrigger_out="triggerReset" />
    </div>
    
    <CetForm
      :data="formData"
      :queryTrigger_in="queryTrigger"
      :saveTrigger_in="saveTrigger"
      :resetTrigger_in="resetTrigger"
      :queryId_in="currentId"
      dataMode="backendInterface"
      :dataConfig="dataConfig"
      @currentData_out="handleDataChange"
      @finishTrigger_out="handleFinish"
      @saveData_out="handleSave"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title"></el-input>
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <el-input type="textarea" v-model="formData.content" :rows="4"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status">
          <el-option label="草稿" value="draft"></el-option>
          <el-option label="发布" value="published"></el-option>
        </el-select>
      </el-form-item>
    </CetForm>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        title: '',
        content: '',
        status: 'draft'
      },
      queryTrigger: 0,
      saveTrigger: 0,
      resetTrigger: 0,
      currentId: -1,
      dataConfig: {
        queryUrl: '/api/articles',
        saveUrl: '/api/articles',
        method: 'POST'
      }
    };
  },
  methods: {
    triggerQuery(timestamp) {
      this.queryTrigger = timestamp;
    },
    triggerSave(timestamp) {
      this.saveTrigger = timestamp;
    },
    triggerReset(timestamp) {
      this.resetTrigger = timestamp;
    },
    handleDataChange(data) {
      console.log('表单数据变化:', data);
    },
    handleFinish(timestamp) {
      console.log('操作完成:', timestamp);
    },
    handleSave(data) {
      console.log('保存数据:', data);
      this.$message.success('保存成功');
    }
  }
};
</script>
```

### 动态表单

```vue
<template>
  <div>
    <CetForm 
      :data="dynamicForm" 
      :rules="dynamicRules"
      @currentData_out="handleFormChange"
    >
      <el-form-item 
        v-for="field in formFields" 
        :key="field.prop"
        :label="field.label" 
        :prop="field.prop"
      >
        <!-- 输入框 -->
        <el-input 
          v-if="field.type === 'input'"
          v-model="dynamicForm[field.prop]"
          :placeholder="field.placeholder"
        />
        
        <!-- 选择器 -->
        <el-select 
          v-else-if="field.type === 'select'"
          v-model="dynamicForm[field.prop]"
          :placeholder="field.placeholder"
        >
          <el-option 
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="dynamicForm[field.prop]"
          type="date"
          :placeholder="field.placeholder"
        />
      </el-form-item>
      
      <el-form-item>
        <CetButton title="添加字段" @statusTrigger_out="addField" />
        <CetButton title="提交" type="primary" @statusTrigger_out="submitDynamicForm" />
      </el-form-item>
    </CetForm>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dynamicForm: {},
      dynamicRules: {},
      formFields: [
        {
          prop: 'name',
          label: '姓名',
          type: 'input',
          placeholder: '请输入姓名'
        },
        {
          prop: 'gender',
          label: '性别',
          type: 'select',
          placeholder: '请选择性别',
          options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' }
          ]
        }
      ]
    };
  },
  created() {
    this.initDynamicForm();
  },
  methods: {
    initDynamicForm() {
      this.formFields.forEach(field => {
        this.$set(this.dynamicForm, field.prop, '');
        this.$set(this.dynamicRules, field.prop, [
          { required: true, message: `请输入${field.label}`, trigger: 'blur' }
        ]);
      });
    },
    addField() {
      const newField = {
        prop: `field_${Date.now()}`,
        label: '新字段',
        type: 'input',
        placeholder: '请输入内容'
      };
      
      this.formFields.push(newField);
      this.$set(this.dynamicForm, newField.prop, '');
      this.$set(this.dynamicRules, newField.prop, [
        { required: true, message: `请输入${newField.label}`, trigger: 'blur' }
      ]);
    },
    handleFormChange(data) {
      console.log('动态表单数据变化:', data);
    },
    submitDynamicForm() {
      console.log('提交动态表单:', this.dynamicForm);
    }
  }
};
</script>
```

### 分步表单

```vue
<template>
  <div>
    <customSteps :active="currentStep" :steps="steps" />
    
    <CetForm 
      :data="stepFormData[currentStep]" 
      :rules="stepRules[currentStep]"
      :ref="`stepForm${currentStep}`"
    >
      <!-- 步骤1：基本信息 -->
      <template v-if="currentStep === 0">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="stepFormData[0].name"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="stepFormData[0].email"></el-input>
        </el-form-item>
      </template>
      
      <!-- 步骤2：详细信息 -->
      <template v-if="currentStep === 1">
        <el-form-item label="地址" prop="address">
          <el-input v-model="stepFormData[1].address"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="stepFormData[1].phone"></el-input>
        </el-form-item>
      </template>
      
      <!-- 步骤3：确认信息 -->
      <template v-if="currentStep === 2">
        <div class="confirm-info">
          <p>姓名: {{ stepFormData[0].name }}</p>
          <p>邮箱: {{ stepFormData[0].email }}</p>
          <p>地址: {{ stepFormData[1].address }}</p>
          <p>电话: {{ stepFormData[1].phone }}</p>
        </div>
      </template>
    </CetForm>
    
    <div class="step-actions">
      <CetButton 
        title="上一步" 
        :disable_in="currentStep === 0"
        @statusTrigger_out="prevStep" 
      />
      <CetButton 
        v-if="currentStep < 2"
        title="下一步" 
        type="primary"
        @statusTrigger_out="nextStep" 
      />
      <CetButton 
        v-else
        title="提交" 
        type="primary"
        @statusTrigger_out="submitStepForm" 
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 0,
      steps: [
        { title: '基本信息', description: '填写基本信息' },
        { title: '详细信息', description: '填写详细信息' },
        { title: '确认提交', description: '确认信息并提交' }
      ],
      stepFormData: [
        { name: '', email: '' },
        { address: '', phone: '' }
      ],
      stepRules: [
        {
          name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
          email: [{ required: true, type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }]
        },
        {
          address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
          phone: [{ required: true, message: '请输入电话', trigger: 'blur' }]
        }
      ]
    };
  },
  methods: {
    nextStep() {
      this.$refs[`stepForm${this.currentStep}`].validate((valid) => {
        if (valid && this.currentStep < 2) {
          this.currentStep++;
        }
      });
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },
    submitStepForm() {
      const allData = Object.assign({}, ...this.stepFormData);
      console.log('提交分步表单:', allData);
      this.$message.success('提交成功');
    }
  }
};
</script>
```

## 适用场景

1. **复杂表单处理**
   - 多字段表单
   - 嵌套表单结构
   - 条件显示字段

2. **数据绑定表单**
   - 后端数据绑定
   - 实时数据同步
   - 数据验证

3. **动态表单生成**
   - 配置化表单
   - 运行时生成字段
   - 自定义表单结构

4. **表单验证和提交**
   - 复杂验证规则
   - 异步验证
   - 分步验证

## 注意事项

1. 使用触发器模式时需要正确设置时间戳
2. 表单验证规则要与表单字段对应
3. 动态表单需要正确处理字段的添加和删除
4. 异步数据加载时要注意组件生命周期
5. 使用 ref 调用组件方法时要确保组件已挂载
6. 表单重置时要清除验证状态

## 相关组件

- [CetButton](./CetButton.md) - 按钮组件
- [CetDialog](./CetDialog.md) - 对话框组件
- [CustomSteps](./CustomSteps.md) - 自定义步骤条
