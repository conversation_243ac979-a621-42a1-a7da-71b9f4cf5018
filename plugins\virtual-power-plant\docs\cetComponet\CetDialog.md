# CetDialog - 对话框组件

## 组件概述

`CetDialog` 是基于 ElementUI Dialog 组件的增强版本，支持触发器模式控制显示/隐藏、拖拽功能和标准的插槽布局。

## 组件名称

- `CetDialog`

## 基本用法

```vue
<template>
  <div>
    <CetButton title="打开对话框" @statusTrigger_out="openDialog" />

    <CetDialog
      :openTrigger_in="openTrigger"
      :closeTrigger_in="closeTrigger"
      title="对话框标题"
      width="800px"
      @openTrigger_out="handleOpen"
      @closeTrigger_out="handleClose"
    >
      <div>对话框内容</div>

      <template #footer>
        <CetButton title="取消" @statusTrigger_out="closeDialog"/>
        <CetButton title="确定" type="primary" @statusTrigger_out="handleConfirm"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      openTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openDialog(timestamp) {
      this.openTrigger = timestamp;
    },
    closeDialog(timestamp) {
      this.closeTrigger = timestamp;
    },
    handleOpen(timestamp) {
      console.log('对话框已打开:', timestamp);
    },
    handleClose(timestamp) {
      console.log('对话框已关闭:', timestamp);
    },
    handleConfirm(timestamp) {
      // 处理确认逻辑
      console.log('确认操作:', timestamp);
      this.closeDialog(timestamp);
    }
  }
};
</script>
```

## 属性说明

### 自定义属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| openTrigger_in | Number | 0 | 打开对话框触发器（时间戳） |
| closeTrigger_in | Number | 0 | 关闭对话框触发器（时间戳） |
| title | String | '弹窗' | 对话框标题 |
| width | String | '960px' | 对话框宽度 |
| closeOnClickModal | Boolean | false | 是否可以通过点击遮罩关闭 |
| closeOnPressEscape | Boolean | false | 是否可以通过按下 ESC 关闭 |
| showClose | Boolean | false | 是否显示关闭按钮 |
| isDraggable | Boolean | - | 是否可拖拽（默认从全局配置获取） |

### 继承属性

继承 ElementUI Dialog 的其他属性：

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modal | Boolean | true | 是否需要遮罩层 |
| modalAppendToBody | Boolean | true | 遮罩层是否插入至 body 元素上 |
| appendToBody | Boolean | false | 是否插入至 body 元素上 |
| lockScroll | Boolean | true | 是否在弹出时将 body 滚动锁定 |
| customClass | String | - | 自定义类名 |
| center | Boolean | false | 是否对头部和底部采用居中布局 |

## 插槽说明

| 插槽名 | 说明 |
|--------|------|
| default | 默认插槽，对话框内容 |
| title | 标题插槽 |
| footer | 底部插槽 |

## 事件说明

### 自定义事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| openTrigger_out | timestamp | 对话框打开后触发，返回时间戳 |
| closeTrigger_out | timestamp | 对话框关闭后触发，返回时间戳 |

### 继承事件

继承 ElementUI Dialog 的所有事件：

- `open`: 打开事件
- `opened`: 打开动画结束事件
- `close`: 关闭事件
- `closed`: 关闭动画结束事件

## 使用示例

### 基础对话框

```vue
<template>
  <div>
    <CetButton title="基础对话框" @statusTrigger_out="openBasic" />
    
    <CetDialog
      :openTrigger_in="basicTrigger"
      :closeTrigger_in="closeTrigger"
      title="基础对话框"
    >
      <p>这是一个基础的对话框内容。</p>
      
      <template #footer>
        <CetButton title="关闭" @statusTrigger_out="closeBasic"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      basicTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openBasic(timestamp) {
      this.basicTrigger = timestamp;
    },
    closeBasic(timestamp) {
      this.closeTrigger = timestamp;
    }
  }
};
</script>
```

### 表单对话框

```vue
<template>
  <div>
    <CetButton title="新增用户" type="primary" @statusTrigger_out="openForm" />
    
    <CetDialog
      :openTrigger_in="formTrigger"
      :closeTrigger_in="closeTrigger"
      title="新增用户"
      width="600px"
    >
      <el-form :model="userForm" :rules="rules" ref="userForm" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="用户" value="user"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <CetButton title="取消" @statusTrigger_out="cancelForm"/>
        <CetButton title="确定" type="primary" @statusTrigger_out="submitForm"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formTrigger: 0,
      closeTrigger: 0,
      userForm: {
        username: '',
        email: '',
        role: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    openForm(timestamp) {
      this.formTrigger = timestamp;
    },
    cancelForm(timestamp) {
      this.resetForm();
      this.closeTrigger = timestamp;
    },
    submitForm(timestamp) {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          // 提交表单
          console.log('提交用户信息:', this.userForm);
          this.$message.success('用户创建成功');
          this.resetForm();
          this.closeTrigger = timestamp;
        }
      });
    },
    resetForm() {
      this.$refs.userForm?.resetFields();
    }
  }
};
</script>
```

### 确认对话框

```vue
<template>
  <div>
    <CetButton title="删除数据" type="danger" @statusTrigger_out="openConfirm" />
    
    <CetDialog
      :openTrigger_in="confirmTrigger"
      :closeTrigger_in="closeTrigger"
      title="确认删除"
      width="400px"
    >
      <div style="text-align: center;">
        <i class="el-icon-warning" style="color: #E6A23C; font-size: 48px;"></i>
        <p style="margin: 20px 0;">确定要删除这条数据吗？</p>
        <p style="color: #999; font-size: 12px;">删除后无法恢复</p>
      </div>
      
      <template #footer>
        <CetButton title="取消" @statusTrigger_out="cancelDelete"/>
        <CetButton title="确定删除" type="danger" @statusTrigger_out="confirmDelete"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      confirmTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openConfirm(timestamp) {
      this.confirmTrigger = timestamp;
    },
    cancelDelete(timestamp) {
      this.closeTrigger = timestamp;
    },
    confirmDelete(timestamp) {
      // 执行删除操作
      console.log('执行删除操作');
      this.$message.success('删除成功');
      this.closeTrigger = timestamp;
    }
  }
};
</script>
```

### 可拖拽对话框

```vue
<template>
  <div>
    <CetButton title="可拖拽对话框" @statusTrigger_out="openDraggable" />
    
    <CetDialog
      :openTrigger_in="draggableTrigger"
      :closeTrigger_in="closeTrigger"
      title="可拖拽对话框"
      :isDraggable="true"
      width="500px"
    >
      <p>这个对话框可以通过拖拽标题栏来移动位置。</p>
      
      <template #footer>
        <CetButton title="关闭" @statusTrigger_out="closeDraggable"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      draggableTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openDraggable(timestamp) {
      this.draggableTrigger = timestamp;
    },
    closeDraggable(timestamp) {
      this.closeTrigger = timestamp;
    }
  }
};
</script>
```

### 自定义标题对话框

```vue
<template>
  <div>
    <CetButton title="自定义标题" @statusTrigger_out="openCustomTitle" />
    
    <CetDialog
      :openTrigger_in="customTitleTrigger"
      :closeTrigger_in="closeTrigger"
      width="600px"
    >
      <template #title>
        <div style="display: flex; align-items: center;">
          <i class="el-icon-info" style="color: #409EFF; margin-right: 8px;"></i>
          <span>自定义标题样式</span>
        </div>
      </template>
      
      <p>这是一个使用自定义标题插槽的对话框。</p>
      
      <template #footer>
        <CetButton title="关闭" @statusTrigger_out="closeCustomTitle"/>
      </template>
    </CetDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      customTitleTrigger: 0,
      closeTrigger: 0
    };
  },
  methods: {
    openCustomTitle(timestamp) {
      this.customTitleTrigger = timestamp;
    },
    closeCustomTitle(timestamp) {
      this.closeTrigger = timestamp;
    }
  }
};
</script>
```

## 主要功能

1. **基于 ElementUI Dialog 的增强组件**
   - 继承所有原生功能
   - 保持API兼容性

2. **触发器模式控制显示/隐藏**
   - 通过时间戳控制对话框状态
   - 便于组件间通信

3. **拖拽功能**
   - 支持通过指令实现拖拽
   - 可通过配置开启/关闭

4. **标准的插槽布局**
   - 标题插槽
   - 内容插槽
   - 底部插槽

## 适用场景

1. **模态对话框**
   - 信息展示
   - 操作确认
   - 状态提示

2. **弹窗表单**
   - 数据录入
   - 信息编辑
   - 设置配置

3. **信息展示窗口**
   - 详情查看
   - 帮助说明
   - 图片预览

4. **确认操作对话框**
   - 删除确认
   - 操作确认
   - 风险提示

## 注意事项

1. 使用触发器模式时，需要正确设置时间戳
2. 默认情况下不能通过点击遮罩或ESC键关闭，需要显式设置
3. 拖拽功能需要在全局配置中启用或通过 `isDraggable` 属性控制
4. 建议在底部插槽中使用 CetButton 组件保持样式一致性
5. 对话框关闭时会自动清理相关状态
6. 大尺寸对话框建议设置合适的最大宽度和高度

## 相关组件

- [CetButton](./CetButton.md) - 按钮组件
- [CetForm](./CetForm.md) - 表单组件
- [UploadDialog](./UploadDialog.md) - 上传对话框
