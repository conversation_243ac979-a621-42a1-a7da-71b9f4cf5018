# CetChart - 图表组件

## 组件概述

`CetChart` 是基于 ECharts 5.3.0 的 Vue 图表组件，提供了统一的图表解决方案，支持多主题切换、数据响应式更新、自动调整大小等功能。

## 组件名称

- `CetChart`

## 版本信息

- cet-chart: v1.7.5
- 基于 ECharts: 5.3.0

## 基本用法

```vue
<template>
  <div class="chart-container">
    <CetChart
      :options="chartOptions"
      :inputData_in="chartData"
      :autoresize="true"
      @click="handleChartClick"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      chartData: [
        { name: '一月', value: 120 },
        { name: '二月', value: 200 },
        { name: '三月', value: 150 }
      ],
      chartOptions: {
        title: {
          text: '月度数据统计'
        },
        tooltip: {
          trigger: 'item'
        },
        xAxis: {
          type: 'category'
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          encode: {
            x: 'name',
            y: 'value'
          }
        }]
      }
    };
  },
  methods: {
    handleChartClick(params) {
      console.log('图表点击事件:', params);
    }
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
}
</style>
```

## 属性说明

### 核心属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| options | Object | - | ECharts 配置选项（必需） |
| inputData_in | Array/Object | - | 图表数据源（可选） |
| initOptions | Object | - | ECharts 初始化选项（可选） |
| group | String | - | 图表分组标识，用于图表联动（可选） |

### 控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| autoresize | Boolean | true | 是否自动调整大小 |
| watchShallow | Boolean | false | 是否浅度监听 options 变化 |
| manualUpdate | Boolean | false | 是否手动更新 |

### 属性详细说明

#### options (必需)
ECharts 的完整配置对象，包含图表的所有配置信息：
```javascript
options: {
  title: { text: '图表标题' },
  tooltip: { trigger: 'axis' },
  legend: { data: ['系列1', '系列2'] },
  xAxis: { type: 'category', data: ['A', 'B', 'C'] },
  yAxis: { type: 'value' },
  series: [
    {
      name: '系列1',
      type: 'line',
      data: [120, 200, 150]
    }
  ]
}
```

#### inputData_in (可选)
数据源，支持数组或对象格式。当提供此属性时，会自动设置 ECharts 的 dataset.source：
```javascript
// 数组格式
inputData_in: [
  { name: '产品A', value: 120, category: '类别1' },
  { name: '产品B', value: 200, category: '类别2' }
]

// 二维数组格式
inputData_in: [
  ['产品', '销量', '类别'],
  ['产品A', 120, '类别1'],
  ['产品B', 200, '类别2']
]
```

## 事件说明

CetChart 支持所有 ECharts 事件，常用事件包括：

### 鼠标事件
- `click`: 点击事件
- `dblclick`: 双击事件
- `mouseover`: 鼠标悬停事件
- `mouseout`: 鼠标离开事件
- `mousemove`: 鼠标移动事件

### 图表事件
- `legendselectchanged`: 图例选择变化
- `datazoom`: 数据缩放事件
- `brush`: 刷选事件
- `rendered`: 渲染完成事件
- `finished`: 动画完成事件

## 组件方法

通过 ref 调用组件方法：

### 数据操作方法
```javascript
this.$refs.chart.mergeOptions(newOptions);  // 合并新配置
this.$refs.chart.appendData(params);        // 追加数据
this.$refs.chart.clear();                   // 清空图表
this.$refs.chart.dispose();                 // 销毁图表
```

### 图表控制方法
```javascript
this.$refs.chart.resize();                  // 手动调整大小
this.$refs.chart.showLoading();             // 显示加载动画
this.$refs.chart.hideLoading();             // 隐藏加载动画
this.$refs.chart.getDataURL();              // 获取图表图片
```

## 使用示例

### 折线图

```vue
<template>
  <div class="chart-container">
    <CetChart :options="lineOptions" :inputData_in="lineData" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      lineData: [
        { date: '2023-01', value: 120 },
        { date: '2023-02', value: 200 },
        { date: '2023-03', value: 150 },
        { date: '2023-04', value: 300 }
      ],
      lineOptions: {
        title: {
          text: '趋势分析'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category'
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'line',
          smooth: true,
          encode: {
            x: 'date',
            y: 'value'
          }
        }]
      }
    };
  }
};
</script>
```

### 柱状图

```vue
<template>
  <div class="chart-container">
    <CetChart :options="barOptions" :inputData_in="barData" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      barData: [
        { category: '产品A', value: 120 },
        { category: '产品B', value: 200 },
        { category: '产品C', value: 150 }
      ],
      barOptions: {
        title: {
          text: '数据对比'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category'
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          encode: {
            x: 'category',
            y: 'value'
          }
        }]
      }
    };
  }
};
</script>
```

### 饼图

```vue
<template>
  <div class="chart-container">
    <CetChart :options="pieOptions" :inputData_in="pieData" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      pieData: [
        { name: '直接访问', value: 335 },
        { name: '邮件营销', value: 310 },
        { name: '联盟广告', value: 234 },
        { name: '视频广告', value: 135 }
      ],
      pieOptions: {
        title: {
          text: '占比分析'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          type: 'pie',
          radius: '50%',
          encode: {
            itemName: 'name',
            value: 'value'
          }
        }]
      }
    };
  }
};
</script>
```

### 仪表盘

```vue
<template>
  <div class="chart-container">
    <CetChart :options="gaugeOptions" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      gaugeOptions: {
        series: [{
          type: 'gauge',
          detail: {
            formatter: '{value}%'
          },
          data: [{
            value: 70,
            name: '完成率'
          }]
        }]
      }
    };
  }
};
</script>
```

### 图表联动

```vue
<template>
  <div>
    <div class="chart-container">
      <CetChart :options="chart1Options" group="group1" @click="handleChart1Click" />
    </div>
    <div class="chart-container">
      <CetChart :options="chart2Options" group="group1" @click="handleChart2Click" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      chart1Options: {
        // 图表1配置
      },
      chart2Options: {
        // 图表2配置
      }
    };
  },
  methods: {
    handleChart1Click(params) {
      console.log('图表1点击:', params);
      // 联动逻辑
    },
    handleChart2Click(params) {
      console.log('图表2点击:', params);
      // 联动逻辑
    }
  }
};
</script>
```

## 主题系统

### 内置主题
- `dark`: 深色主题
- `light`: 浅色主题
- `blue`: 蓝色主题
- `bluex`: 深蓝主题

### 主题自动切换
主题会根据 `localStorage` 中的 `omega_theme` 值自动切换。

### 自定义主题
```javascript
import { registerTheme } from "cet-chart";

// 注册自定义主题
registerTheme('custom', {
  color: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
  backgroundColor: '#f8f9fa',
  textStyle: {
    color: '#333'
  }
});
```

## 数据绑定模式

### 使用 inputData_in (推荐)
```vue
<CetChart :options="options" :inputData_in="chartData" />
```

### 直接在 options 中配置数据
```vue
<CetChart :options="options" />
```

## 适用场景

1. **数据可视化**
   - 统计图表
   - 趋势分析
   - 数据对比

2. **实时监控**
   - 实时数据展示
   - 状态监控
   - 性能指标

3. **报表展示**
   - 业务报表
   - 分析报告
   - 数据仪表板

## 注意事项

1. **容器尺寸**：确保图表容器有明确的宽高，否则图表可能无法正常显示
2. **数据格式**：使用 `inputData_in` 时，确保数据格式与 `encode` 配置匹配
3. **主题切换**：主题切换会触发图表重新初始化，注意性能影响
4. **内存管理**：组件销毁时会自动清理图表实例，无需手动处理
5. **事件处理**：避免在事件处理函数中进行大量计算，影响交互性能

## 相关组件

- [CetTable](./CetTable.md) - 表格组件
- [CetDialog](./CetDialog.md) - 对话框组件
