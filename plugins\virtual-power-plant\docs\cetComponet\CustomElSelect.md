# CustomElSelect - 自定义选择器

## 组件概述

`CustomElSelect` 是基于 ElementUI Select 组件的增强版本，提供了带前缀标签和提示信息的下拉选择器功能。

## 组件名称

- `CustomElSelect`
- `customElSelect`

## 基本用法

```vue
<template>
  <div>
    <CustomElSelect 
      prefix_in="选择类型" 
      popover_in="这是一个提示信息"
      v-model="selectedValue"
    >
      <el-option label="选项1" value="1"></el-option>
      <el-option label="选项2" value="2"></el-option>
    </CustomElSelect>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedValue: ''
    };
  }
};
</script>
```

## 属性说明

### 自定义属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| prefix_in | String | - | 前缀标签文本 |
| popover_in | String | - | 悬停提示内容（可选） |

### 继承属性

继承 ElementUI Select 的所有属性和事件，包括但不限于：

- `value / v-model`: 绑定值
- `multiple`: 是否多选
- `disabled`: 是否禁用
- `clearable`: 是否可以清空选项
- `placeholder`: 占位符
- `filterable`: 是否可搜索
- `size`: 输入框尺寸

## 事件说明

继承 ElementUI Select 的所有事件：

- `change`: 选中值发生变化时触发
- `visible-change`: 下拉框出现/隐藏时触发
- `remove-tag`: 多选模式下移除tag时触发
- `clear`: 可清空的单选模式下用户点击清空按钮时触发
- `blur`: 当 input 失去焦点时触发
- `focus`: 当 input 获得焦点时触发

## 使用示例

### 基础选择器

```vue
<CustomElSelect 
  prefix_in="状态" 
  v-model="status"
  placeholder="请选择状态"
>
  <el-option label="启用" value="1"></el-option>
  <el-option label="禁用" value="0"></el-option>
</CustomElSelect>
```

### 带提示信息的选择器

```vue
<CustomElSelect 
  prefix_in="优先级" 
  popover_in="选择任务的优先级别，高优先级任务会优先处理"
  v-model="priority"
>
  <el-option label="高" value="high"></el-option>
  <el-option label="中" value="medium"></el-option>
  <el-option label="低" value="low"></el-option>
</CustomElSelect>
```

### 多选模式

```vue
<CustomElSelect 
  prefix_in="标签" 
  v-model="tags"
  multiple
  placeholder="请选择标签"
>
  <el-option label="前端" value="frontend"></el-option>
  <el-option label="后端" value="backend"></el-option>
  <el-option label="测试" value="test"></el-option>
</CustomElSelect>
```

### 可搜索选择器

```vue
<CustomElSelect 
  prefix_in="用户" 
  v-model="userId"
  filterable
  placeholder="搜索用户"
>
  <el-option 
    v-for="user in users" 
    :key="user.id"
    :label="user.name" 
    :value="user.id"
  ></el-option>
</CustomElSelect>
```

## 样式特性

- 带边框和圆角的自定义样式
- 前缀标签与选择器的统一视觉效果
- 问号提示图标的悬停交互
- 与项目整体UI风格保持一致

## 适用场景

1. **需要带前缀标签的下拉选择器**
   - 表单中需要明确标识选择项用途的场景
   - 需要统一标签样式的选择器

2. **需要问号提示图标的选择器**
   - 选择项需要额外说明的场景
   - 用户可能不理解选择项含义的情况

3. **自定义样式的选择器**
   - 需要与项目UI风格保持一致的选择器
   - 需要特殊视觉效果的选择器

## 注意事项

1. `prefix_in` 属性是必需的，用于显示前缀标签
2. `popover_in` 属性是可选的，只有在需要提示信息时才使用
3. 组件完全继承 ElementUI Select 的功能，可以使用所有原生属性和事件
4. 样式已经预设，通常不需要额外的CSS调整
5. 在表单验证中，验证规则应该绑定到 v-model 的值上

## 相关组件

- [CustomElDatePicker](./CustomElDatePicker.md) - 自定义日期选择器
- [CetSimpleSelect](./CetSimpleSelect.md) - 简单选择器
- [CetSelectTree](./CetSelectTree.md) - 树形选择器
