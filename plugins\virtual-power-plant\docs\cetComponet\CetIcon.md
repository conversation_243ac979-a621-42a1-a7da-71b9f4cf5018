# CetIcon - 图标组件

## 组件概述

`CetIcon` 是一个SVG图标组件，用于显示项目中的图标资源，支持多种尺寸和交互效果。

## 组件名称

- `CetIcon`

## 基本用法

```vue
<template>
  <div>
    <CetIcon
      iconClass="test"
      class="I2 hover p2"
    />
  </div>
</template>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| iconClass | String | - | 图标类名（必需），对应 SVG symbol 的 ID |

## 样式类说明

### 尺寸类
- `I1`: 最小尺寸图标
- `I2`: 小尺寸图标
- `I3`: 中等尺寸图标
- `I4`: 大尺寸图标
- `I5`: 最大尺寸图标

### 交互类
- `hover`: 悬停效果
- `active`: 激活状态

### 内边距类
- `p0`: 无内边距
- `p1`: 1级内边距
- `p2`: 2级内边距
- `p3`: 3级内边距
- `p4`: 4级内边距
- `p5`: 5级内边距
- `p6`: 6级内边距
- `p7`: 7级内边距
- `p8`: 8级内边距

## 使用示例

### 基础图标

```vue
<template>
  <div>
    <!-- 基础图标 -->
    <CetIcon iconClass="home" />
    
    <!-- 不同尺寸的图标 -->
    <CetIcon iconClass="user" class="I1" />
    <CetIcon iconClass="user" class="I2" />
    <CetIcon iconClass="user" class="I3" />
    <CetIcon iconClass="user" class="I4" />
    <CetIcon iconClass="user" class="I5" />
  </div>
</template>
```

### 带交互效果的图标

```vue
<template>
  <div>
    <!-- 悬停效果 -->
    <CetIcon iconClass="search" class="I3 hover" />
    
    <!-- 激活状态 -->
    <CetIcon iconClass="star" class="I3 active" />
    
    <!-- 组合效果 -->
    <CetIcon iconClass="heart" class="I3 hover active" />
  </div>
</template>
```

### 按钮中的图标

```vue
<template>
  <div>
    <CetButton title="搜索" type="primary">
      <CetIcon iconClass="search" class="I2" />
    </CetButton>
    
    <CetButton title="编辑" type="success">
      <CetIcon iconClass="edit" class="I2" />
    </CetButton>
    
    <CetButton title="删除" type="danger">
      <CetIcon iconClass="delete" class="I2" />
    </CetButton>
  </div>
</template>
```

### 导航图标

```vue
<template>
  <div class="navigation">
    <div class="nav-item" @click="goHome">
      <CetIcon iconClass="home" class="I3 hover p2" />
      <span>首页</span>
    </div>
    
    <div class="nav-item" @click="goProfile">
      <CetIcon iconClass="user" class="I3 hover p2" />
      <span>个人中心</span>
    </div>
    
    <div class="nav-item" @click="goSettings">
      <CetIcon iconClass="settings" class="I3 hover p2" />
      <span>设置</span>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goHome() {
      this.$router.push('/home');
    },
    goProfile() {
      this.$router.push('/profile');
    },
    goSettings() {
      this.$router.push('/settings');
    }
  }
};
</script>

<style scoped>
.navigation {
  display: flex;
  gap: 20px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-item:hover {
  background-color: #f5f5f5;
}

.nav-item span {
  margin-top: 5px;
  font-size: 12px;
}
</style>
```

### 状态指示图标

```vue
<template>
  <div>
    <div class="status-list">
      <div class="status-item">
        <CetIcon iconClass="success" class="I2 p1" style="color: #67C23A;" />
        <span>成功状态</span>
      </div>
      
      <div class="status-item">
        <CetIcon iconClass="warning" class="I2 p1" style="color: #E6A23C;" />
        <span>警告状态</span>
      </div>
      
      <div class="status-item">
        <CetIcon iconClass="error" class="I2 p1" style="color: #F56C6C;" />
        <span>错误状态</span>
      </div>
      
      <div class="status-item">
        <CetIcon iconClass="info" class="I2 p1" style="color: #909399;" />
        <span>信息状态</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.status-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
```

### 表格操作图标

```vue
<template>
  <div>
    <CetTable :data="tableData">
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <div class="table-actions">
            <CetIcon 
              iconClass="edit" 
              class="I2 hover p1" 
              @click="editRow(scope.row)"
              title="编辑"
            />
            <CetIcon 
              iconClass="delete" 
              class="I2 hover p1" 
              @click="deleteRow(scope.row)"
              title="删除"
              style="color: #F56C6C;"
            />
            <CetIcon 
              iconClass="view" 
              class="I2 hover p1" 
              @click="viewRow(scope.row)"
              title="查看"
            />
          </div>
        </template>
      </el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', email: '<EMAIL>' },
        { id: 2, name: '李四', email: '<EMAIL>' }
      ]
    };
  },
  methods: {
    editRow(row) {
      console.log('编辑:', row);
    },
    deleteRow(row) {
      console.log('删除:', row);
    },
    viewRow(row) {
      console.log('查看:', row);
    }
  }
};
</script>

<style scoped>
.table-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.table-actions .cet-icon {
  cursor: pointer;
  transition: transform 0.2s;
}

.table-actions .cet-icon:hover {
  transform: scale(1.1);
}
</style>
```

### 动态图标

```vue
<template>
  <div>
    <div class="dynamic-icons">
      <div class="icon-group">
        <h4>加载状态</h4>
        <CetIcon 
          iconClass="loading" 
          class="I3 p2" 
          :class="{ 'rotating': isLoading }"
        />
        <CetButton 
          title="切换加载状态" 
          @statusTrigger_out="toggleLoading"
        />
      </div>
      
      <div class="icon-group">
        <h4>收藏状态</h4>
        <CetIcon 
          :iconClass="isFavorited ? 'heart-filled' : 'heart'" 
          class="I3 hover p2" 
          :style="{ color: isFavorited ? '#F56C6C' : '#909399' }"
          @click="toggleFavorite"
        />
      </div>
      
      <div class="icon-group">
        <h4>展开/收起</h4>
        <CetIcon 
          :iconClass="isExpanded ? 'arrow-up' : 'arrow-down'" 
          class="I3 hover p2" 
          @click="toggleExpand"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      isFavorited: false,
      isExpanded: false
    };
  },
  methods: {
    toggleLoading() {
      this.isLoading = !this.isLoading;
    },
    toggleFavorite() {
      this.isFavorited = !this.isFavorited;
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>

<style scoped>
.dynamic-icons {
  display: flex;
  gap: 30px;
}

.icon-group {
  text-align: center;
}

.icon-group h4 {
  margin-bottom: 10px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.cet-icon {
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
```

## 图标管理

### 图标注册

```javascript
// 在项目中注册新图标
// 1. 将SVG文件添加到图标库
// 2. 确保SVG有正确的symbol ID
// 3. 在组件中使用对应的iconClass

// 示例：注册自定义图标
const customIcon = `
<symbol id="custom-icon" viewBox="0 0 24 24">
  <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/>
</symbol>
`;
```

### 图标库维护

```javascript
// 图标库结构
icons/
├── common/          // 通用图标
│   ├── home.svg
│   ├── user.svg
│   └── settings.svg
├── business/        // 业务图标
│   ├── order.svg
│   ├── product.svg
│   └── customer.svg
└── status/          // 状态图标
    ├── success.svg
    ├── warning.svg
    └── error.svg
```

## 适用场景

1. **SVG 图标展示**
   - 矢量图标显示
   - 可缩放图标
   - 高清图标

2. **按钮图标**
   - 操作按钮图标
   - 功能图标
   - 装饰图标

3. **状态指示图标**
   - 成功/失败状态
   - 警告提示
   - 信息指示

4. **导航图标**
   - 菜单图标
   - 导航指示
   - 页面标识

## 注意事项

1. `iconClass` 属性是必需的，必须对应已注册的SVG symbol ID
2. 图标尺寸通过CSS类控制，不要直接设置width/height
3. 图标颜色可以通过CSS的color属性控制
4. 使用交互效果时注意性能影响
5. 确保图标在不同主题下的显示效果
6. 图标应该具有语义化的命名

## 相关组件

- [CetButton](./CetButton.md) - 按钮组件
- [CetTable](./CetTable.md) - 表格组件
