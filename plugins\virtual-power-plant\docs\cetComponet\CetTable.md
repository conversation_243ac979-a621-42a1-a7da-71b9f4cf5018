# CetTable - 表格组件

## 组件概述

`CetTable` 是基于 ElementUI Table 组件的增强版本，内置分页器、支持排序筛选、行选择多选、数据导出、树形数据展示等功能。

## 组件名称

- `CetTable`

## 基本用法

```vue
<template>
  <div>
    <CetTable
      :data.sync="tableData"
      :dynamicInput.sync="dynamicInput"
      queryMode="trigger"
      dataMode="component"
      :dataConfig="dataConfig"
      :showPagination="true"
      :border="true"
      :highlightCurrentRow="true"
      @outputData_out="handleDataOutput"
      @currentChange_out="handleCurrentChange"
    >
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="age" label="年龄"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25, email: 'z<PERSON><PERSON>@example.com' },
        { id: 2, name: '李四', age: 30, email: '<EMAIL>' }
      ],
      dynamicInput: {},
      dataConfig: {
        // 数据配置
      }
    };
  },
  methods: {
    handleDataOutput(data) {
      console.log('表格数据输出:', data);
    },
    handleCurrentChange(row) {
      console.log('当前行变化:', row);
    }
  }
};
</script>
```

## 属性说明

### 核心属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Array | [] | 表格数据（支持 .sync） |
| dynamicInput | Object | {} | 动态查询条件（支持 .sync） |
| queryMode | String | 'trigger' | 查询模式（trigger/diff） |
| dataMode | String | 'component' | 数据获取模式（backendInterface/component/static） |
| dataConfig | Object | {} | 数据绑定配置 |

### 显示控制属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showPagination | Boolean | true | 是否显示分页器 |
| showPaginationUI | Boolean | true | 是否显示分页UI |
| border | Boolean | true | 是否显示边框 |
| highlightCurrentRow | Boolean | true | 是否高亮当前行 |
| isTreeData | Boolean | false | 是否为树形数据 |

### 配置属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| tableKey | String | 'id' | 表格主键字段 |
| tooltipEffect | String | - | tooltip 效果 |
| paginationCfg | Object | {} | 分页器配置 |
| exportFileName | String/Function | - | 导出文件名 |
| exportMultiHeader | Function | - | 多级表头导出配置 |
| isColumsHeaderDoLayout | Boolean | - | 是否自动调整列宽 |

## 触发器属性

| 属性名 | 类型 | 说明 |
|--------|------|------|
| queryTrigger_in | Number | 查询触发器 |
| deleteTrigger_in | Number | 删除触发器 |
| localDeleteTrigger_in | Number | 本地删除触发器 |
| batchDeletionTrigger_in | Number | 批量删除触发器 |
| localBatchDeletionTrigger_in | Number | 本地批量删除触发器 |
| refreshTrigger_in | Number | 刷新触发器 |
| clearTrigger_in | Number | 清空触发器 |
| exportTrigger_in | Number | 导出触发器 |
| doLayoutTrigger_in | Number | 重新布局触发器 |

## 数据输入属性

| 属性名 | 类型 | 说明 |
|--------|------|------|
| addData_in | Object | 新增数据 |
| editData_in | Object | 编辑数据 |
| multiSelectData_in | Array | 设置多选数据 |
| selectRowData_in | Object | 设置选中行数据 |
| queryNode_in | Object | 查询节点输入 |

## 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| outputData_out | data | 数据输出事件 |
| currentChange_out | row | 当前行变化事件 |
| multiSelectData_out | selection | 多选数据变化事件 |
| deleteData_out | row | 删除数据事件 |
| finishTrigger_out | timestamp | 操作完成事件 |

## 使用示例

### 基础表格

```vue
<template>
  <div>
    <CetTable :data="users" :showPagination="false">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '激活' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      users: [
        { id: 1, name: '张三', email: '<EMAIL>', status: 'active' },
        { id: 2, name: '李四', email: '<EMAIL>', status: 'inactive' }
      ]
    };
  }
};
</script>
```

### 带分页的表格

```vue
<template>
  <div>
    <CetTable
      :data="tableData"
      :showPagination="true"
      :paginationCfg="paginationConfig"
      @outputData_out="handlePageChange"
    >
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="department" label="部门"></el-table-column>
      <el-table-column prop="position" label="职位"></el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      paginationConfig: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
    };
  },
  methods: {
    handlePageChange(data) {
      console.log('分页数据:', data);
      // 处理分页逻辑
    }
  }
};
</script>
```

### 多选表格

```vue
<template>
  <div>
    <div class="table-actions">
      <CetButton 
        title="批量删除" 
        type="danger"
        :disable_in="selectedRows.length === 0"
        @statusTrigger_out="batchDelete"
      />
    </div>
    
    <CetTable
      :data="tableData"
      @multiSelectData_out="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', email: '<EMAIL>' },
        { id: 2, name: '李四', email: '<EMAIL>' }
      ],
      selectedRows: []
    };
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedRows = selection;
      console.log('选中的行:', selection);
    },
    batchDelete(timestamp) {
      if (this.selectedRows.length > 0) {
        console.log('批量删除:', this.selectedRows);
        // 执行批量删除逻辑
      }
    }
  }
};
</script>
```

### 带操作列的表格

```vue
<template>
  <div>
    <CetTable :data="tableData">
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <CetButton 
            title="编辑" 
            size="mini" 
            type="primary"
            @statusTrigger_out="editRow(scope.row)"
          />
          <CetButton 
            title="删除" 
            size="mini" 
            type="danger"
            @statusTrigger_out="deleteRow(scope.row)"
          />
        </template>
      </el-table-column>
    </CetTable>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', email: '<EMAIL>' },
        { id: 2, name: '李四', email: '<EMAIL>' }
      ]
    };
  },
  methods: {
    editRow(row) {
      console.log('编辑行:', row);
      // 打开编辑对话框
    },
    deleteRow(row) {
      console.log('删除行:', row);
      // 确认删除操作
    }
  }
};
</script>
```

## 主要功能

1. **内置分页器**
   - 自动分页处理
   - 可配置分页参数
   - 支持分页事件

2. **支持排序、筛选**
   - 列排序功能
   - 数据筛选
   - 自定义筛选条件

3. **支持行选择和多选**
   - 单行选择
   - 多行选择
   - 选择状态管理

4. **支持数据导出**
   - Excel导出
   - 自定义导出格式
   - 多级表头导出

5. **支持树形数据展示**
   - 树形结构显示
   - 展开/收起功能
   - 层级数据处理

6. **继承 ElementUI Table 的所有功能**
   - 完整的表格功能
   - 自定义列模板
   - 表格样式控制

## 适用场景

1. **数据列表展示**
   - 用户列表
   - 订单列表
   - 产品列表

2. **复杂表格操作**
   - 数据编辑
   - 批量操作
   - 状态管理

3. **带分页的数据表格**
   - 大数据量展示
   - 分页查询
   - 数据统计

4. **树形数据表格**
   - 组织架构
   - 分类数据
   - 层级关系

## 注意事项

1. 使用 `.sync` 修饰符时要确保父组件正确处理数据变化
2. 触发器属性使用时间戳进行状态管理
3. 分页配置需要与后端接口保持一致
4. 树形数据需要正确设置 `isTreeData` 属性
5. 导出功能需要配置相应的文件名和格式
6. 大数据量时建议使用分页和懒加载

## 相关组件

- [CetButton](./CetButton.md) - 按钮组件
- [CetDialog](./CetDialog.md) - 对话框组件
- [CetForm](./CetForm.md) - 表单组件
