# 公共组件库使用指南

本文档详细介绍了项目中使用的三个核心组件库：`eem-base`、`cet-common` 和 `cet-chart` 的使用规范和适用范围。

## 组件库概览

### EEM-BASE 组件库
基础UI组件库，提供常用的表单控件和交互组件：
- [CustomElSelect](./CustomElSelect.md) - 自定义选择器
- [CustomElDatePicker](./CustomElDatePicker.md) - 自定义日期选择器
- [CustomSteps](./CustomSteps.md) - 自定义步骤条
- [UploadDialog](./UploadDialog.md) - 上传对话框

### CET-COMMON 组件库
通用业务组件库，提供复杂的业务组件：

**已完成文档：**
- [CetButton](./CetButton.md) - 按钮组件 ✅
- [CetDialog](./CetDialog.md) - 对话框组件 ✅
- [CetTable](./CetTable.md) - 表格组件 ✅
- [CetForm](./CetForm.md) - 表单组件 ✅
- [CetDateSelect](./CetDateSelect.md) - 日期选择组件 ✅
- [CetTree](./CetTree.md) - 树形组件 ✅
- [CetIcon](./CetIcon.md) - 图标组件 ✅

**待完成文档：**
- CetSelectTree - 树形选择器
- CetTabs - 标签页组件
- CetTransfer - 穿梭框组件
- CetAside - 侧边栏组件
- CetGiantTree - 大数据量树形组件
- CetSimpleSelect - 简单选择器
- CetZtree - 基于zTree的树形组件

### CET-CHART 图表组件库
图表组件库，基于ECharts提供统一的图表解决方案：
- [CetChart](./CetChart.md) - 图表组件

## 安装和配置

### 全局安装配置

在 main.js 中的配置示例：

```javascript
import CetCommon from "cet-common";
import CetChart from "cet-chart";
import { CustomElSelect, CustomElDatePicker } from "eem-base/components";

// 注册 eem-base 组件
Vue.component("CustomElDatePicker", CustomElDatePicker);
Vue.component("customElSelect", CustomElSelect);
Vue.component("CustomElSelect", CustomElSelect);

// 安装 cet-common
Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  }
});

// 安装 cet-chart
Vue.use(CetChart, {
  themeConf: {
    // 自定义主题配置
  }
});
```

### 默认配置说明

通过 `defaultSettings` 可以配置组件默认行为：

- `CetDialog.isDraggable`: 对话框是否可拖拽
- `CetTable.isColumsHeaderDoLayout`: 表格列头是否重新布局
- `CetGiantTree.isAutoCancelSelected`: 大树是否自动取消选择
- `CetDateSelect.isClosedEnd`: 日期选择是否闭合结束

## 使用规范

### 命名规范
- 组件名使用 PascalCase
- 属性名使用 camelCase 或 snake_case（根据组件要求）
- 事件名使用 camelCase

### 使用建议
1. 优先使用组件库提供的组件，保持界面一致性
2. 合理使用触发器模式进行组件间通信
3. 充分利用组件的插槽功能进行自定义
4. 注意组件的生命周期和数据绑定

### 注意事项
- 触发器属性通常使用时间戳进行状态管理
- 组件数据绑定时注意深度监听和浅度监听的区别
- 使用 .sync 修饰符时要确保父组件正确处理数据变化
- 异步数据加载时要注意组件的生命周期
- 表格和树组件的大数据量处理要合理使用分页和懒加载

## 相关文档

- [全局配置](./全局配置.md) - 详细的全局配置说明
- [最佳实践](./最佳实践.md) - 使用建议和注意事项

## 版本信息

- eem-base: 基础组件库
- cet-common: 通用业务组件库
- cet-chart: v1.7.5 (基于 ECharts 5.3.0)
