# CustomSteps - 自定义步骤条

## 组件概述

`CustomSteps` 是一个自定义的步骤条组件，用于显示多步骤流程的进度和状态。

## 组件名称

- `customSteps`

## 基本用法

```vue
<template>
  <div>
    <customSteps 
      :active="currentStep" 
      :steps="stepList"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 1,
      stepList: [
        { title: '第一步', description: '填写基本信息' },
        { title: '第二步', description: '确认信息' },
        { title: '第三步', description: '完成' }
      ]
    };
  }
};
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| steps | Array | - | 步骤数据（必需） |
| active | Number | 0 | 当前激活步骤索引 |

### steps 数据格式

每个步骤对象包含以下属性：

| 属性名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| title | String | 是 | 步骤标题 |
| description | String | 否 | 步骤描述 |

## 使用示例

### 基础步骤条

```vue
<template>
  <div>
    <customSteps 
      :active="currentStep" 
      :steps="basicSteps"
    />
    
    <div class="step-content">
      <div v-if="currentStep === 0">
        <h3>填写基本信息</h3>
        <p>请填写您的基本信息...</p>
      </div>
      <div v-else-if="currentStep === 1">
        <h3>确认信息</h3>
        <p>请确认您填写的信息...</p>
      </div>
      <div v-else-if="currentStep === 2">
        <h3>完成</h3>
        <p>信息提交成功！</p>
      </div>
    </div>
    
    <div class="step-actions">
      <button @click="prevStep" :disabled="currentStep === 0">上一步</button>
      <button @click="nextStep" :disabled="currentStep === basicSteps.length - 1">下一步</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 0,
      basicSteps: [
        { title: '填写信息', description: '填写基本信息' },
        { title: '确认信息', description: '确认填写的信息' },
        { title: '完成', description: '提交完成' }
      ]
    };
  },
  methods: {
    nextStep() {
      if (this.currentStep < this.basicSteps.length - 1) {
        this.currentStep++;
      }
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    }
  }
};
</script>
```

### 表单向导步骤条

```vue
<template>
  <div>
    <customSteps 
      :active="formStep" 
      :steps="formSteps"
    />
    
    <div class="form-container">
      <!-- 步骤1：基本信息 -->
      <div v-show="formStep === 0">
        <h3>基本信息</h3>
        <el-form :model="formData.basic">
          <el-form-item label="姓名">
            <el-input v-model="formData.basic.name"></el-input>
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="formData.basic.email"></el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 步骤2：详细信息 -->
      <div v-show="formStep === 1">
        <h3>详细信息</h3>
        <el-form :model="formData.detail">
          <el-form-item label="地址">
            <el-input v-model="formData.detail.address"></el-input>
          </el-form-item>
          <el-form-item label="电话">
            <el-input v-model="formData.detail.phone"></el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 步骤3：确认提交 -->
      <div v-show="formStep === 2">
        <h3>确认信息</h3>
        <div class="confirm-info">
          <p>姓名：{{ formData.basic.name }}</p>
          <p>邮箱：{{ formData.basic.email }}</p>
          <p>地址：{{ formData.detail.address }}</p>
          <p>电话：{{ formData.detail.phone }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formStep: 0,
      formSteps: [
        { title: '基本信息', description: '填写基本个人信息' },
        { title: '详细信息', description: '填写详细联系信息' },
        { title: '确认提交', description: '确认信息并提交' }
      ],
      formData: {
        basic: {
          name: '',
          email: ''
        },
        detail: {
          address: '',
          phone: ''
        }
      }
    };
  }
};
</script>
```

### 订单处理步骤条

```vue
<template>
  <div>
    <customSteps 
      :active="orderStatus" 
      :steps="orderSteps"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderStatus: 2, // 当前订单状态
      orderSteps: [
        { title: '下单', description: '订单已创建' },
        { title: '付款', description: '等待付款' },
        { title: '发货', description: '商品已发货' },
        { title: '配送', description: '正在配送中' },
        { title: '完成', description: '订单已完成' }
      ]
    };
  }
};
</script>
```

### 审批流程步骤条

```vue
<template>
  <div>
    <customSteps 
      :active="approvalStep" 
      :steps="approvalSteps"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      approvalStep: 1,
      approvalSteps: [
        { title: '提交申请', description: '申请已提交' },
        { title: '部门审核', description: '部门主管审核中' },
        { title: '财务审核', description: '财务部门审核中' },
        { title: '总经理审批', description: '总经理最终审批' },
        { title: '审批完成', description: '审批流程完成' }
      ]
    };
  }
};
</script>
```

## 样式特性

- 清晰的步骤指示器
- 当前步骤高亮显示
- 已完成步骤的视觉反馈
- 步骤间的连接线
- 响应式设计，适配不同屏幕尺寸

## 适用场景

1. **多步骤流程展示**
   - 表单向导
   - 注册流程
   - 设置向导

2. **向导式操作界面**
   - 复杂配置的分步设置
   - 多页面表单的进度指示
   - 安装向导

3. **进度指示器**
   - 订单状态跟踪
   - 审批流程展示
   - 任务进度显示

4. **状态展示**
   - 工作流状态
   - 处理进度
   - 完成度指示

## 注意事项

1. `steps` 属性是必需的，必须是一个数组
2. 每个步骤对象必须包含 `title` 属性
3. `description` 属性是可选的，用于提供额外的步骤说明
4. `active` 属性从0开始计数，表示当前激活的步骤索引
5. 组件只负责显示步骤状态，步骤切换逻辑需要在父组件中实现
6. 建议为步骤条设置合适的容器宽度，以确保步骤显示完整

## 最佳实践

1. **步骤数量控制**：建议步骤数量控制在3-7个之间，过多会影响用户体验
2. **步骤标题简洁**：步骤标题应该简洁明了，能够清楚表达当前步骤的目的
3. **描述信息有用**：如果使用描述信息，应该提供有价值的补充说明
4. **状态同步**：确保步骤条的状态与实际业务流程保持同步
5. **错误处理**：在步骤切换时进行必要的验证，防止用户跳过必要步骤

## 相关组件

- [CetTabs](./CetTabs.md) - 标签页组件
- [CetForm](./CetForm.md) - 表单组件
